@echo off
chcp 65001 >nul
title 华为/荣耀设备保修查询工具 - 完整打包流程

echo ========================================
echo 华为/荣耀设备保修查询工具
echo 完整打包流程
echo ========================================
echo.

echo 步骤1: 检查环境
echo ----------------------------------------

echo 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ✗ 错误: 未检测到Python环境
    echo   请先安装Python 3.8或更高版本
    echo   下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo ✓ Python环境检查通过: %PYTHON_VERSION%

echo 检查PyInstaller...
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo PyInstaller未安装，正在安装...
    pip install pyinstaller
    if errorlevel 1 (
        echo ✗ PyInstaller安装失败
        pause
        exit /b 1
    )
    echo ✓ PyInstaller安装成功
) else (
    echo ✓ PyInstaller已安装
)

echo.
echo 步骤2: 检查项目文件
echo ----------------------------------------

set MISSING_FILES=0

if not exist "honor_warranty_app.py" (
    echo ✗ 主程序文件 honor_warranty_app.py 不存在
    set MISSING_FILES=1
) else (
    echo ✓ 主程序文件存在
)

if not exist "honor_warranty_querier.py" (
    echo ✗ 查询模块 honor_warranty_querier.py 不存在
    set MISSING_FILES=1
) else (
    echo ✓ 查询模块存在
)

if not exist "huawei_new2_update_3.py" (
    echo ✗ 华为模块 huawei_new2_update_3.py 不存在
    set MISSING_FILES=1
) else (
    echo ✓ 华为模块存在
)

if not exist "disclaimer_txt.py" (
    echo ✗ 免责声明 disclaimer_txt.py 不存在
    set MISSING_FILES=1
) else (
    echo ✓ 免责声明存在
)

if not exist "honor_logo.ico" (
    echo ! 警告: 图标文件 honor_logo.ico 不存在
) else (
    echo ✓ 图标文件存在
)

if %MISSING_FILES%==1 (
    echo.
    echo ✗ 关键文件缺失，无法继续打包
    pause
    exit /b 1
)

echo.
echo 步骤3: 开始打包
echo ----------------------------------------

echo 正在执行打包脚本...
python build_exe.py

if errorlevel 1 (
    echo.
    echo ✗ 打包失败
    pause
    exit /b 1
)

echo.
echo 步骤4: 测试打包结果
echo ----------------------------------------

echo 正在测试打包结果...
python test_build.py

echo.
echo 步骤5: 完成
echo ----------------------------------------

if exist "dist\华为荣耀设备保修查询工具.exe" (
    echo ✓ 打包成功完成！
    echo.
    echo 生成的文件位置: dist\
    echo.
    echo 主要文件:
    echo   - 华为荣耀设备保修查询工具.exe  (主程序)
    echo   - 启动程序.bat                   (启动脚本)
    echo   - check_dependencies.py          (依赖检查)
    echo   - 使用说明.txt                   (使用说明)
    echo   - 测试报告.txt                   (测试报告)
    echo.
    echo 用户使用方法:
    echo   1. 确保已安装Python 3.8+
    echo   2. 双击运行"启动程序.bat"
    echo   3. 首次运行会自动安装依赖
    echo.
    echo 是否要打开输出目录？
    set /p OPEN_DIR="输入 y 打开目录，其他键跳过: "
    if /i "%OPEN_DIR%"=="y" (
        explorer dist
    )
) else (
    echo ✗ 打包失败，未找到生成的exe文件
)

echo.
echo ========================================
echo 打包流程完成
echo ========================================

pause
