('E:\\env\\honor_exe\\compile_tools\\build\\华为荣耀设备保修查询工具\\PYZ-00.pyz',
 [('PIL',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BdfFontFile',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\BdfFontFile.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.ContainerIO',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\ContainerIO.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FontFile',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\FontFile.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GdImageFile',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\GdImageFile.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageEnhance',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\ImageEnhance.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageGrab',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\ImageGrab.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageMorph',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\ImageMorph.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageStat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\ImageStat.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageTransform',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\ImageTransform.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PSDraw',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\PSDraw.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcfFontFile',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\PcfFontFile.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TarIO',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\TarIO.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WalImageFile',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\WalImageFile.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.__main__',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\__main__.py',
   'PYMODULE'),
  ('PIL._binary',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._tkinter_finder',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\_tkinter_finder.py',
   'PYMODULE'),
  ('PIL._typing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.report',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PIL\\report.py',
   'PYMODULE'),
  ('PyInstaller',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyInstaller\\__init__.py',
   'PYMODULE'),
  ('PyInstaller._shared_with_waf',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyInstaller\\_shared_with_waf.py',
   'PYMODULE'),
  ('PyInstaller.building',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyInstaller\\building\\__init__.py',
   'PYMODULE'),
  ('PyInstaller.building.utils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyInstaller\\building\\utils.py',
   'PYMODULE'),
  ('PyInstaller.compat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyInstaller\\compat.py',
   'PYMODULE'),
  ('PyInstaller.config',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyInstaller\\config.py',
   'PYMODULE'),
  ('PyInstaller.depend',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyInstaller\\depend\\__init__.py',
   'PYMODULE'),
  ('PyInstaller.depend.imphookapi',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyInstaller\\depend\\imphookapi.py',
   'PYMODULE'),
  ('PyInstaller.exceptions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyInstaller\\exceptions.py',
   'PYMODULE'),
  ('PyInstaller.isolated',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyInstaller\\isolated\\__init__.py',
   'PYMODULE'),
  ('PyInstaller.isolated._parent',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyInstaller\\isolated\\_parent.py',
   'PYMODULE'),
  ('PyInstaller.lib',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyInstaller\\lib\\__init__.py',
   'PYMODULE'),
  ('PyInstaller.lib.modulegraph',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyInstaller\\lib\\modulegraph\\__init__.py',
   'PYMODULE'),
  ('PyInstaller.lib.modulegraph.modulegraph',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyInstaller\\lib\\modulegraph\\modulegraph.py',
   'PYMODULE'),
  ('PyInstaller.lib.modulegraph.util',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyInstaller\\lib\\modulegraph\\util.py',
   'PYMODULE'),
  ('PyInstaller.log',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyInstaller\\log.py',
   'PYMODULE'),
  ('PyInstaller.utils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyInstaller\\utils\\__init__.py',
   'PYMODULE'),
  ('PyInstaller.utils.hooks',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyInstaller\\utils\\hooks\\__init__.py',
   'PYMODULE'),
  ('PyInstaller.utils.hooks.conda',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyInstaller\\utils\\hooks\\conda.py',
   'PYMODULE'),
  ('PyInstaller.utils.misc',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyInstaller\\utils\\misc.py',
   'PYMODULE'),
  ('PyInstaller.utils.osx',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyInstaller\\utils\\osx.py',
   'PYMODULE'),
  ('PyInstaller.utils.win32',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyInstaller\\utils\\win32\\__init__.py',
   'PYMODULE'),
  ('PyInstaller.utils.win32.versioninfo',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyInstaller\\utils\\win32\\versioninfo.py',
   'PYMODULE'),
  ('PyQt6',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\__init__.py',
   'PYMODULE'),
  ('PyQt6.lupdate',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\lupdate\\__init__.py',
   'PYMODULE'),
  ('PyQt6.lupdate.designer_source',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\lupdate\\designer_source.py',
   'PYMODULE'),
  ('PyQt6.lupdate.lupdate',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\lupdate\\lupdate.py',
   'PYMODULE'),
  ('PyQt6.lupdate.pylupdate',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\lupdate\\pylupdate.py',
   'PYMODULE'),
  ('PyQt6.lupdate.python_source',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\lupdate\\python_source.py',
   'PYMODULE'),
  ('PyQt6.lupdate.source_file',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\lupdate\\source_file.py',
   'PYMODULE'),
  ('PyQt6.lupdate.translation_file',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\lupdate\\translation_file.py',
   'PYMODULE'),
  ('PyQt6.lupdate.translations',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\lupdate\\translations.py',
   'PYMODULE'),
  ('PyQt6.lupdate.user',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\lupdate\\user.py',
   'PYMODULE'),
  ('PyQt6.uic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\uic\\__init__.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\__init__.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.as_string',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\as_string.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.compiler',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\compiler.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.indenter',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\indenter.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.misc',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\misc.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.proxy_metaclass',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\proxy_metaclass.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.qobjectcreator',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\qobjectcreator.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.qtproxies',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\qtproxies.py',
   'PYMODULE'),
  ('PyQt6.uic.Loader',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\uic\\Loader\\__init__.py',
   'PYMODULE'),
  ('PyQt6.uic.Loader.loader',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\uic\\Loader\\loader.py',
   'PYMODULE'),
  ('PyQt6.uic.Loader.qobjectcreator',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\uic\\Loader\\qobjectcreator.py',
   'PYMODULE'),
  ('PyQt6.uic.compile_ui',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\uic\\compile_ui.py',
   'PYMODULE'),
  ('PyQt6.uic.enum_map',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\uic\\enum_map.py',
   'PYMODULE'),
  ('PyQt6.uic.exceptions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\uic\\exceptions.py',
   'PYMODULE'),
  ('PyQt6.uic.icon_cache',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\uic\\icon_cache.py',
   'PYMODULE'),
  ('PyQt6.uic.load_ui',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\uic\\load_ui.py',
   'PYMODULE'),
  ('PyQt6.uic.objcreator',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\uic\\objcreator.py',
   'PYMODULE'),
  ('PyQt6.uic.properties',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\uic\\properties.py',
   'PYMODULE'),
  ('PyQt6.uic.pyuic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\uic\\pyuic.py',
   'PYMODULE'),
  ('PyQt6.uic.ui_file',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\uic\\ui_file.py',
   'PYMODULE'),
  ('PyQt6.uic.uiparser',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyQt6\\uic\\uiparser.py',
   'PYMODULE'),
  ('__future__', 'E:\\python\\Lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'E:\\python\\Lib\\_aix_support.py', 'PYMODULE'),
  ('_compat_pickle', 'E:\\python\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'E:\\python\\Lib\\_compression.py', 'PYMODULE'),
  ('_distutils_hack',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_py_abc', 'E:\\python\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'E:\\python\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'E:\\python\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_sitebuiltins', 'E:\\python\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('_strptime', 'E:\\python\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'E:\\python\\Lib\\_threading_local.py', 'PYMODULE'),
  ('aiohappyeyeballs',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohappyeyeballs\\__init__.py',
   'PYMODULE'),
  ('aiohappyeyeballs._staggered',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohappyeyeballs\\_staggered.py',
   'PYMODULE'),
  ('aiohappyeyeballs.impl',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohappyeyeballs\\impl.py',
   'PYMODULE'),
  ('aiohappyeyeballs.types',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohappyeyeballs\\types.py',
   'PYMODULE'),
  ('aiohappyeyeballs.utils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohappyeyeballs\\utils.py',
   'PYMODULE'),
  ('aiohttp',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\__init__.py',
   'PYMODULE'),
  ('aiohttp._websocket',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\_websocket\\__init__.py',
   'PYMODULE'),
  ('aiohttp._websocket.helpers',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\_websocket\\helpers.py',
   'PYMODULE'),
  ('aiohttp._websocket.models',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\_websocket\\models.py',
   'PYMODULE'),
  ('aiohttp._websocket.reader',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\_websocket\\reader.py',
   'PYMODULE'),
  ('aiohttp._websocket.reader_py',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\_websocket\\reader_py.py',
   'PYMODULE'),
  ('aiohttp._websocket.writer',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\_websocket\\writer.py',
   'PYMODULE'),
  ('aiohttp.abc',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\abc.py',
   'PYMODULE'),
  ('aiohttp.base_protocol',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\base_protocol.py',
   'PYMODULE'),
  ('aiohttp.client',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\client.py',
   'PYMODULE'),
  ('aiohttp.client_exceptions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\client_exceptions.py',
   'PYMODULE'),
  ('aiohttp.client_proto',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\client_proto.py',
   'PYMODULE'),
  ('aiohttp.client_reqrep',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\client_reqrep.py',
   'PYMODULE'),
  ('aiohttp.client_ws',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\client_ws.py',
   'PYMODULE'),
  ('aiohttp.compression_utils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\compression_utils.py',
   'PYMODULE'),
  ('aiohttp.connector',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\connector.py',
   'PYMODULE'),
  ('aiohttp.cookiejar',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\cookiejar.py',
   'PYMODULE'),
  ('aiohttp.formdata',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\formdata.py',
   'PYMODULE'),
  ('aiohttp.hdrs',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\hdrs.py',
   'PYMODULE'),
  ('aiohttp.helpers',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\helpers.py',
   'PYMODULE'),
  ('aiohttp.http',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\http.py',
   'PYMODULE'),
  ('aiohttp.http_exceptions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\http_exceptions.py',
   'PYMODULE'),
  ('aiohttp.http_parser',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\http_parser.py',
   'PYMODULE'),
  ('aiohttp.http_websocket',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\http_websocket.py',
   'PYMODULE'),
  ('aiohttp.http_writer',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\http_writer.py',
   'PYMODULE'),
  ('aiohttp.log',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\log.py',
   'PYMODULE'),
  ('aiohttp.multipart',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\multipart.py',
   'PYMODULE'),
  ('aiohttp.payload',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\payload.py',
   'PYMODULE'),
  ('aiohttp.payload_streamer',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\payload_streamer.py',
   'PYMODULE'),
  ('aiohttp.resolver',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\resolver.py',
   'PYMODULE'),
  ('aiohttp.streams',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\streams.py',
   'PYMODULE'),
  ('aiohttp.tcp_helpers',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\tcp_helpers.py',
   'PYMODULE'),
  ('aiohttp.tracing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\tracing.py',
   'PYMODULE'),
  ('aiohttp.typedefs',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\typedefs.py',
   'PYMODULE'),
  ('aiohttp.web',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\web.py',
   'PYMODULE'),
  ('aiohttp.web_app',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\web_app.py',
   'PYMODULE'),
  ('aiohttp.web_exceptions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\web_exceptions.py',
   'PYMODULE'),
  ('aiohttp.web_fileresponse',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\web_fileresponse.py',
   'PYMODULE'),
  ('aiohttp.web_log',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\web_log.py',
   'PYMODULE'),
  ('aiohttp.web_middlewares',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\web_middlewares.py',
   'PYMODULE'),
  ('aiohttp.web_protocol',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\web_protocol.py',
   'PYMODULE'),
  ('aiohttp.web_request',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\web_request.py',
   'PYMODULE'),
  ('aiohttp.web_response',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\web_response.py',
   'PYMODULE'),
  ('aiohttp.web_routedef',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\web_routedef.py',
   'PYMODULE'),
  ('aiohttp.web_runner',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\web_runner.py',
   'PYMODULE'),
  ('aiohttp.web_server',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\web_server.py',
   'PYMODULE'),
  ('aiohttp.web_urldispatcher',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\web_urldispatcher.py',
   'PYMODULE'),
  ('aiohttp.web_ws',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\web_ws.py',
   'PYMODULE'),
  ('aiohttp.worker',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiohttp\\worker.py',
   'PYMODULE'),
  ('aiosignal',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\aiosignal\\__init__.py',
   'PYMODULE'),
  ('altgraph',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\altgraph\\__init__.py',
   'PYMODULE'),
  ('altgraph.Graph',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\altgraph\\Graph.py',
   'PYMODULE'),
  ('altgraph.GraphUtil',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\altgraph\\GraphUtil.py',
   'PYMODULE'),
  ('altgraph.ObjectGraph',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\altgraph\\ObjectGraph.py',
   'PYMODULE'),
  ('argparse', 'E:\\python\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'E:\\python\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'E:\\python\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'E:\\python\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'E:\\python\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'E:\\python\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks', 'E:\\python\\Lib\\asyncio\\base_tasks.py', 'PYMODULE'),
  ('asyncio.constants', 'E:\\python\\Lib\\asyncio\\constants.py', 'PYMODULE'),
  ('asyncio.coroutines', 'E:\\python\\Lib\\asyncio\\coroutines.py', 'PYMODULE'),
  ('asyncio.events', 'E:\\python\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.exceptions', 'E:\\python\\Lib\\asyncio\\exceptions.py', 'PYMODULE'),
  ('asyncio.format_helpers',
   'E:\\python\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures', 'E:\\python\\Lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.locks', 'E:\\python\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'E:\\python\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins', 'E:\\python\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'E:\\python\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols', 'E:\\python\\Lib\\asyncio\\protocols.py', 'PYMODULE'),
  ('asyncio.queues', 'E:\\python\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'E:\\python\\Lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.selector_events',
   'E:\\python\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto', 'E:\\python\\Lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.staggered', 'E:\\python\\Lib\\asyncio\\staggered.py', 'PYMODULE'),
  ('asyncio.streams', 'E:\\python\\Lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.subprocess', 'E:\\python\\Lib\\asyncio\\subprocess.py', 'PYMODULE'),
  ('asyncio.taskgroups', 'E:\\python\\Lib\\asyncio\\taskgroups.py', 'PYMODULE'),
  ('asyncio.tasks', 'E:\\python\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.threads', 'E:\\python\\Lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.timeouts', 'E:\\python\\Lib\\asyncio\\timeouts.py', 'PYMODULE'),
  ('asyncio.transports', 'E:\\python\\Lib\\asyncio\\transports.py', 'PYMODULE'),
  ('asyncio.trsock', 'E:\\python\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'E:\\python\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'E:\\python\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'E:\\python\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('attr',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\attr\\__init__.py',
   'PYMODULE'),
  ('attr._cmp',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\attr\\_cmp.py',
   'PYMODULE'),
  ('attr._compat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\attr\\_compat.py',
   'PYMODULE'),
  ('attr._config',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\attr\\_config.py',
   'PYMODULE'),
  ('attr._funcs',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\attr\\_funcs.py',
   'PYMODULE'),
  ('attr._make',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\attr\\_make.py',
   'PYMODULE'),
  ('attr._next_gen',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\attr\\_next_gen.py',
   'PYMODULE'),
  ('attr._version_info',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\attr\\_version_info.py',
   'PYMODULE'),
  ('attr.converters',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\attr\\converters.py',
   'PYMODULE'),
  ('attr.exceptions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\attr\\exceptions.py',
   'PYMODULE'),
  ('attr.filters',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\attr\\filters.py',
   'PYMODULE'),
  ('attr.setters',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\attr\\setters.py',
   'PYMODULE'),
  ('attr.validators',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\attr\\validators.py',
   'PYMODULE'),
  ('backports',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'E:\\python\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'E:\\python\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'E:\\python\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'E:\\python\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'E:\\python\\Lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('charset_normalizer',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd', 'E:\\python\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'E:\\python\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'E:\\python\\Lib\\codeop.py', 'PYMODULE'),
  ('colorsys', 'E:\\python\\Lib\\colorsys.py', 'PYMODULE'),
  ('concurrent', 'E:\\python\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('concurrent.futures',
   'E:\\python\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'E:\\python\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'E:\\python\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'E:\\python\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser', 'E:\\python\\Lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'E:\\python\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'E:\\python\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'E:\\python\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'E:\\python\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'E:\\python\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'E:\\python\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian', 'E:\\python\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('ctypes.macholib',
   'E:\\python\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'E:\\python\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'E:\\python\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'E:\\python\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'E:\\python\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes.wintypes', 'E:\\python\\Lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('dataclasses', 'E:\\python\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'E:\\python\\Lib\\datetime.py', 'PYMODULE'),
  ('dateutil',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('ddddocr',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\ddddocr\\__init__.py',
   'PYMODULE'),
  ('decimal', 'E:\\python\\Lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'E:\\python\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'E:\\python\\Lib\\dis.py', 'PYMODULE'),
  ('disclaimer_txt',
   'E:\\env\\honor_exe\\compile_tools\\disclaimer_txt.py',
   'PYMODULE'),
  ('doctest', 'E:\\python\\Lib\\doctest.py', 'PYMODULE'),
  ('email', 'E:\\python\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'E:\\python\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'E:\\python\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'E:\\python\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase', 'E:\\python\\Lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.base64mime', 'E:\\python\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'E:\\python\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'E:\\python\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'E:\\python\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'E:\\python\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'E:\\python\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'E:\\python\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'E:\\python\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'E:\\python\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'E:\\python\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'E:\\python\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.mime', 'E:\\python\\Lib\\email\\mime\\__init__.py', 'PYMODULE'),
  ('email.mime.base', 'E:\\python\\Lib\\email\\mime\\base.py', 'PYMODULE'),
  ('email.mime.multipart',
   'E:\\python\\Lib\\email\\mime\\multipart.py',
   'PYMODULE'),
  ('email.mime.nonmultipart',
   'E:\\python\\Lib\\email\\mime\\nonmultipart.py',
   'PYMODULE'),
  ('email.mime.text', 'E:\\python\\Lib\\email\\mime\\text.py', 'PYMODULE'),
  ('email.parser', 'E:\\python\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'E:\\python\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'E:\\python\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'E:\\python\\Lib\\email\\utils.py', 'PYMODULE'),
  ('et_xmlfile',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('fileinput', 'E:\\python\\Lib\\fileinput.py', 'PYMODULE'),
  ('fnmatch', 'E:\\python\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'E:\\python\\Lib\\fractions.py', 'PYMODULE'),
  ('frozenlist',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\frozenlist\\__init__.py',
   'PYMODULE'),
  ('ftplib', 'E:\\python\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'E:\\python\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'E:\\python\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'E:\\python\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'E:\\python\\Lib\\glob.py', 'PYMODULE'),
  ('greenlet',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\greenlet\\__init__.py',
   'PYMODULE'),
  ('gzip', 'E:\\python\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'E:\\python\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'E:\\python\\Lib\\hmac.py', 'PYMODULE'),
  ('honor_warranty_querier',
   'E:\\env\\honor_exe\\compile_tools\\honor_warranty_querier.py',
   'PYMODULE'),
  ('html', 'E:\\python\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'E:\\python\\Lib\\html\\entities.py', 'PYMODULE'),
  ('http', 'E:\\python\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'E:\\python\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'E:\\python\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.cookies', 'E:\\python\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('http.server', 'E:\\python\\Lib\\http\\server.py', 'PYMODULE'),
  ('huawei_new2_update_3',
   'E:\\env\\honor_exe\\compile_tools\\huawei_new2_update_3.py',
   'PYMODULE'),
  ('idna',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib', 'E:\\python\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'E:\\python\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'E:\\python\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'E:\\python\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'E:\\python\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'E:\\python\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'E:\\python\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'E:\\python\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'E:\\python\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'E:\\python\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'E:\\python\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'E:\\python\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'E:\\python\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers', 'E:\\python\\Lib\\importlib\\readers.py', 'PYMODULE'),
  ('importlib.resources',
   'E:\\python\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'E:\\python\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'E:\\python\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'E:\\python\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'E:\\python\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'E:\\python\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'E:\\python\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'E:\\python\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('importlib_metadata',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('importlib_metadata._typing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\importlib_metadata\\_typing.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py311',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('inspect', 'E:\\python\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'E:\\python\\Lib\\ipaddress.py', 'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('json', 'E:\\python\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'E:\\python\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'E:\\python\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'E:\\python\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'E:\\python\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('logging.handlers', 'E:\\python\\Lib\\logging\\handlers.py', 'PYMODULE'),
  ('lzma', 'E:\\python\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'E:\\python\\Lib\\mimetypes.py', 'PYMODULE'),
  ('more_itertools',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('more_itertools.more',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\more_itertools\\more.py',
   'PYMODULE'),
  ('more_itertools.recipes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('multidict',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\multidict\\__init__.py',
   'PYMODULE'),
  ('multidict._abc',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\multidict\\_abc.py',
   'PYMODULE'),
  ('multidict._compat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\multidict\\_compat.py',
   'PYMODULE'),
  ('multidict._multidict_py',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\multidict\\_multidict_py.py',
   'PYMODULE'),
  ('multiprocessing',
   'E:\\python\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'E:\\python\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'E:\\python\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'E:\\python\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'E:\\python\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'E:\\python\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'E:\\python\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'E:\\python\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'E:\\python\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'E:\\python\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'E:\\python\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'E:\\python\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'E:\\python\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'E:\\python\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'E:\\python\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'E:\\python\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'E:\\python\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'E:\\python\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'E:\\python\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'E:\\python\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'E:\\python\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'E:\\python\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'E:\\python\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'E:\\python\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'E:\\python\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'E:\\python\\Lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._configtool',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_configtool.py',
   'PYMODULE'),
  ('numpy._core',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.cversions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\cversions.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pyinstaller',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_pyinstaller\\__init__.py',
   'PYMODULE'),
  ('numpy._pyinstaller.hook-numpy',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_pyinstaller\\hook-numpy.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._extended_precision',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_typing\\_extended_precision.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy._utils._pep440',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\_utils\\_pep440.py',
   'PYMODULE'),
  ('numpy.char',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.compat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.compat.tests',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\compat\\tests\\__init__.py',
   'PYMODULE'),
  ('numpy.conftest',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\conftest.py',
   'PYMODULE'),
  ('numpy.core',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._multiarray_umath',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\core\\_multiarray_umath.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.core.records',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__main__',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\f2py\\__main__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py._src_pyf',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\f2py\\_src_pyf.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft.tests',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\fft\\tests\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.tests.test_helper',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\fft\\tests\\test_helper.py',
   'PYMODULE'),
  ('numpy.fft.tests.test_pocketfft',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\fft\\tests\\test_pocketfft.py',
   'PYMODULE'),
  ('numpy.lib',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._user_array_impl',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\_user_array_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.tests',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\tests\\__init__.py',
   'PYMODULE'),
  ('numpy.lib.tests.test__datasource',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\tests\\test__datasource.py',
   'PYMODULE'),
  ('numpy.lib.tests.test__iotools',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\tests\\test__iotools.py',
   'PYMODULE'),
  ('numpy.lib.tests.test__version',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\tests\\test__version.py',
   'PYMODULE'),
  ('numpy.lib.tests.test_array_utils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\tests\\test_array_utils.py',
   'PYMODULE'),
  ('numpy.lib.tests.test_arraypad',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\tests\\test_arraypad.py',
   'PYMODULE'),
  ('numpy.lib.tests.test_arraysetops',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\tests\\test_arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.tests.test_arrayterator',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\tests\\test_arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.tests.test_format',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\tests\\test_format.py',
   'PYMODULE'),
  ('numpy.lib.tests.test_function_base',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\tests\\test_function_base.py',
   'PYMODULE'),
  ('numpy.lib.tests.test_histograms',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\tests\\test_histograms.py',
   'PYMODULE'),
  ('numpy.lib.tests.test_index_tricks',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\tests\\test_index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.tests.test_io',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\tests\\test_io.py',
   'PYMODULE'),
  ('numpy.lib.tests.test_loadtxt',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\tests\\test_loadtxt.py',
   'PYMODULE'),
  ('numpy.lib.tests.test_mixins',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\tests\\test_mixins.py',
   'PYMODULE'),
  ('numpy.lib.tests.test_nanfunctions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\tests\\test_nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.tests.test_packbits',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\tests\\test_packbits.py',
   'PYMODULE'),
  ('numpy.lib.tests.test_polynomial',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\tests\\test_polynomial.py',
   'PYMODULE'),
  ('numpy.lib.tests.test_recfunctions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\tests\\test_recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.tests.test_regression',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\tests\\test_regression.py',
   'PYMODULE'),
  ('numpy.lib.tests.test_shape_base',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\tests\\test_shape_base.py',
   'PYMODULE'),
  ('numpy.lib.tests.test_stride_tricks',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\tests\\test_stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.tests.test_twodim_base',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\tests\\test_twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.tests.test_type_check',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\tests\\test_type_check.py',
   'PYMODULE'),
  ('numpy.lib.tests.test_ufunclike',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\tests\\test_ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.tests.test_utils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\tests\\test_utils.py',
   'PYMODULE'),
  ('numpy.lib.user_array',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\lib\\user_array.py',
   'PYMODULE'),
  ('numpy.linalg',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.linalg.tests',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\linalg\\tests\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.tests.test_deprecations',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\linalg\\tests\\test_deprecations.py',
   'PYMODULE'),
  ('numpy.linalg.tests.test_linalg',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\linalg\\tests\\test_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.tests.test_regression',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\linalg\\tests\\test_regression.py',
   'PYMODULE'),
  ('numpy.ma',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.ma.tests',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\ma\\tests\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.tests.test_arrayobject',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\ma\\tests\\test_arrayobject.py',
   'PYMODULE'),
  ('numpy.ma.tests.test_core',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\ma\\tests\\test_core.py',
   'PYMODULE'),
  ('numpy.ma.tests.test_deprecations',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\ma\\tests\\test_deprecations.py',
   'PYMODULE'),
  ('numpy.ma.tests.test_extras',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\ma\\tests\\test_extras.py',
   'PYMODULE'),
  ('numpy.ma.tests.test_mrecords',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\ma\\tests\\test_mrecords.py',
   'PYMODULE'),
  ('numpy.ma.tests.test_old_ma',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\ma\\tests\\test_old_ma.py',
   'PYMODULE'),
  ('numpy.ma.tests.test_regression',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\ma\\tests\\test_regression.py',
   'PYMODULE'),
  ('numpy.ma.tests.test_subclassing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\ma\\tests\\test_subclassing.py',
   'PYMODULE'),
  ('numpy.ma.testutils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\ma\\testutils.py',
   'PYMODULE'),
  ('numpy.ma.timer_comparison',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\ma\\timer_comparison.py',
   'PYMODULE'),
  ('numpy.matlib',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.matrixlib.tests',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\matrixlib\\tests\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.tests.test_defmatrix',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\matrixlib\\tests\\test_defmatrix.py',
   'PYMODULE'),
  ('numpy.matrixlib.tests.test_interaction',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\matrixlib\\tests\\test_interaction.py',
   'PYMODULE'),
  ('numpy.matrixlib.tests.test_masked_matrix',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\matrixlib\\tests\\test_masked_matrix.py',
   'PYMODULE'),
  ('numpy.matrixlib.tests.test_matrix_linalg',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\matrixlib\\tests\\test_matrix_linalg.py',
   'PYMODULE'),
  ('numpy.matrixlib.tests.test_multiarray',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\matrixlib\\tests\\test_multiarray.py',
   'PYMODULE'),
  ('numpy.matrixlib.tests.test_numeric',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\matrixlib\\tests\\test_numeric.py',
   'PYMODULE'),
  ('numpy.matrixlib.tests.test_regression',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\matrixlib\\tests\\test_regression.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.polynomial.tests',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\polynomial\\tests\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial.tests.test_chebyshev',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\polynomial\\tests\\test_chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.tests.test_classes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\polynomial\\tests\\test_classes.py',
   'PYMODULE'),
  ('numpy.polynomial.tests.test_hermite',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\polynomial\\tests\\test_hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.tests.test_hermite_e',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\polynomial\\tests\\test_hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.tests.test_laguerre',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\polynomial\\tests\\test_laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.tests.test_legendre',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\polynomial\\tests\\test_legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.tests.test_polynomial',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\polynomial\\tests\\test_polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.tests.test_polyutils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\polynomial\\tests\\test_polyutils.py',
   'PYMODULE'),
  ('numpy.polynomial.tests.test_printing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\polynomial\\tests\\test_printing.py',
   'PYMODULE'),
  ('numpy.polynomial.tests.test_symbol',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\polynomial\\tests\\test_symbol.py',
   'PYMODULE'),
  ('numpy.random',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._examples', '-', 'PYMODULE'),
  ('numpy.random._examples.cffi', '-', 'PYMODULE'),
  ('numpy.random._examples.cffi.extending',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\random\\_examples\\cffi\\extending.py',
   'PYMODULE'),
  ('numpy.random._examples.cffi.parse',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\random\\_examples\\cffi\\parse.py',
   'PYMODULE'),
  ('numpy.random._examples.numba', '-', 'PYMODULE'),
  ('numpy.random._examples.numba.extending',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\random\\_examples\\numba\\extending.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.random.tests',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\random\\tests\\__init__.py',
   'PYMODULE'),
  ('numpy.random.tests.data',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\random\\tests\\data\\__init__.py',
   'PYMODULE'),
  ('numpy.random.tests.test_direct',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\random\\tests\\test_direct.py',
   'PYMODULE'),
  ('numpy.random.tests.test_extending',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\random\\tests\\test_extending.py',
   'PYMODULE'),
  ('numpy.random.tests.test_generator_mt19937',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\random\\tests\\test_generator_mt19937.py',
   'PYMODULE'),
  ('numpy.random.tests.test_generator_mt19937_regressions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\random\\tests\\test_generator_mt19937_regressions.py',
   'PYMODULE'),
  ('numpy.random.tests.test_random',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\random\\tests\\test_random.py',
   'PYMODULE'),
  ('numpy.random.tests.test_randomstate',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\random\\tests\\test_randomstate.py',
   'PYMODULE'),
  ('numpy.random.tests.test_randomstate_regression',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\random\\tests\\test_randomstate_regression.py',
   'PYMODULE'),
  ('numpy.random.tests.test_regression',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\random\\tests\\test_regression.py',
   'PYMODULE'),
  ('numpy.random.tests.test_seed_sequence',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\random\\tests\\test_seed_sequence.py',
   'PYMODULE'),
  ('numpy.random.tests.test_smoke',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\random\\tests\\test_smoke.py',
   'PYMODULE'),
  ('numpy.rec',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.testing.print_coercion_tables',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\testing\\print_coercion_tables.py',
   'PYMODULE'),
  ('numpy.testing.tests',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\testing\\tests\\__init__.py',
   'PYMODULE'),
  ('numpy.testing.tests.test_utils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\testing\\tests\\test_utils.py',
   'PYMODULE'),
  ('numpy.tests',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\tests\\__init__.py',
   'PYMODULE'),
  ('numpy.tests.test__all__',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\tests\\test__all__.py',
   'PYMODULE'),
  ('numpy.tests.test_configtool',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\tests\\test_configtool.py',
   'PYMODULE'),
  ('numpy.tests.test_ctypeslib',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\tests\\test_ctypeslib.py',
   'PYMODULE'),
  ('numpy.tests.test_lazyloading',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\tests\\test_lazyloading.py',
   'PYMODULE'),
  ('numpy.tests.test_matlib',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\tests\\test_matlib.py',
   'PYMODULE'),
  ('numpy.tests.test_numpy_config',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\tests\\test_numpy_config.py',
   'PYMODULE'),
  ('numpy.tests.test_numpy_version',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\tests\\test_numpy_version.py',
   'PYMODULE'),
  ('numpy.tests.test_public_api',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\tests\\test_public_api.py',
   'PYMODULE'),
  ('numpy.tests.test_reloading',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\tests\\test_reloading.py',
   'PYMODULE'),
  ('numpy.tests.test_scripts',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\tests\\test_scripts.py',
   'PYMODULE'),
  ('numpy.tests.test_warnings',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\tests\\test_warnings.py',
   'PYMODULE'),
  ('numpy.typing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.typing.mypy_plugin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\typing\\mypy_plugin.py',
   'PYMODULE'),
  ('numpy.typing.tests',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\typing\\tests\\__init__.py',
   'PYMODULE'),
  ('numpy.typing.tests.test_isfile',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\typing\\tests\\test_isfile.py',
   'PYMODULE'),
  ('numpy.typing.tests.test_runtime',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\typing\\tests\\test_runtime.py',
   'PYMODULE'),
  ('numpy.typing.tests.test_typing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\typing\\tests\\test_typing.py',
   'PYMODULE'),
  ('numpy.version',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('onnxruntime',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\onnxruntime\\__init__.py',
   'PYMODULE'),
  ('onnxruntime.capi',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\onnxruntime\\capi\\__init__.py',
   'PYMODULE'),
  ('onnxruntime.capi._ld_preload',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\onnxruntime\\capi\\_ld_preload.py',
   'PYMODULE'),
  ('onnxruntime.capi._pybind_state',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\onnxruntime\\capi\\_pybind_state.py',
   'PYMODULE'),
  ('onnxruntime.capi.onnxruntime_collect_build_info',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\onnxruntime\\capi\\onnxruntime_collect_build_info.py',
   'PYMODULE'),
  ('onnxruntime.capi.onnxruntime_inference_collection',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\onnxruntime\\capi\\onnxruntime_inference_collection.py',
   'PYMODULE'),
  ('onnxruntime.capi.onnxruntime_validation',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\onnxruntime\\capi\\onnxruntime_validation.py',
   'PYMODULE'),
  ('onnxruntime.capi.version_info',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\onnxruntime\\capi\\version_info.py',
   'PYMODULE'),
  ('opcode', 'E:\\python\\Lib\\opcode.py', 'PYMODULE'),
  ('openpyxl',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.abc',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\compat\\abc.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.product',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\compat\\product.py',
   'PYMODULE'),
  ('openpyxl.compat.singleton',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\compat\\singleton.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.descriptors.slots',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\descriptors\\slots.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.interface',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\packaging\\interface.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.dataframe',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\utils\\dataframe.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.inference',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\utils\\inference.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_watch',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\cell_watch.py',
   'PYMODULE'),
  ('openpyxl.worksheet.controls',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\controls.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.custom',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.errors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\errors.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.ole',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\ole.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.picture',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\picture.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.smart_tag',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\smart_tag.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('ordlookup',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\ordlookup\\__init__.py',
   'PYMODULE'),
  ('ordlookup.oleaut32',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\ordlookup\\oleaut32.py',
   'PYMODULE'),
  ('ordlookup.ws2_32',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\ordlookup\\ws2_32.py',
   'PYMODULE'),
  ('packaging',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pandas',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._config',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._hypothesis',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\_testing\\_hypothesis.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.api',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.conftest',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\conftest.py',
   'PYMODULE'),
  ('pandas.core',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.sparse',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.sparse.api',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\sparse\\api.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.boxplot',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\boxplot.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.converter',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\converter.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.core',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.groupby',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\groupby.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.hist',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\hist.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.misc',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\misc.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.style',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\style.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.timeseries',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\timeseries.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.tools',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\tools.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tests',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.api',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.api.test_api',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\api\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.api.test_types',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\api\\test_types.py',
   'PYMODULE'),
  ('pandas.tests.apply',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\apply\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.apply.common',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\apply\\common.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_frame_apply',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\apply\\test_frame_apply.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_frame_apply_relabeling',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\apply\\test_frame_apply_relabeling.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_frame_transform',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\apply\\test_frame_transform.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_invalid_arg',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\apply\\test_invalid_arg.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_numba',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\apply\\test_numba.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_series_apply',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\apply\\test_series_apply.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_series_apply_relabeling',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\apply\\test_series_apply_relabeling.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_series_transform',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\apply\\test_series_transform.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_str',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\apply\\test_str.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arithmetic\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.common',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arithmetic\\common.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.conftest',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arithmetic\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_array_ops',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_array_ops.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_categorical',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_datetime64',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_datetime64.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_interval',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_interval.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_numeric',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_numeric.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_object',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_object.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_period',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_period.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_timedelta64',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_timedelta64.py',
   'PYMODULE'),
  ('pandas.tests.arrays',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_arithmetic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_astype',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_comparison',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_comparison.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_construction',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_construction.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_function',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_function.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_indexing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_logical',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_logical.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_ops',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_ops.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_reduction',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_reduction.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_repr',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_repr.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_algos',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_algos.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_analytics',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_analytics.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_api',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_astype',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_constructors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_dtypes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_indexing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_map',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_map.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_missing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_missing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_operators',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_operators.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_replace',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_replace.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_repr',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_repr.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_sorting',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_sorting.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_subclass',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_subclass.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_take',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_take.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_warnings',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_warnings.py',
   'PYMODULE'),
  ('pandas.tests.arrays.datetimes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\datetimes\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.datetimes.test_constructors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\datetimes\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.arrays.datetimes.test_cumulative',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\datetimes\\test_cumulative.py',
   'PYMODULE'),
  ('pandas.tests.arrays.datetimes.test_reductions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\datetimes\\test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.conftest',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_arithmetic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_astype',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_comparison',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_comparison.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_concat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_concat.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_construction',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_construction.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_contains',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_contains.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_function',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_function.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_repr',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_repr.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_to_numpy',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_to_numpy.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.conftest',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_arithmetic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_comparison',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_comparison.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_concat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_concat.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_construction',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_construction.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_dtypes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_function',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_function.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_indexing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_reduction',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_reduction.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_repr',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_repr.py',
   'PYMODULE'),
  ('pandas.tests.arrays.interval',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\interval\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.interval.test_astype',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\interval\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.interval.test_formats',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\interval\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.arrays.interval.test_interval',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\interval\\test_interval.py',
   'PYMODULE'),
  ('pandas.tests.arrays.interval.test_interval_pyarrow',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\interval\\test_interval_pyarrow.py',
   'PYMODULE'),
  ('pandas.tests.arrays.interval.test_overlaps',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\interval\\test_overlaps.py',
   'PYMODULE'),
  ('pandas.tests.arrays.masked',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\masked\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.masked.test_arithmetic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\masked\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.arrays.masked.test_arrow_compat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\masked\\test_arrow_compat.py',
   'PYMODULE'),
  ('pandas.tests.arrays.masked.test_function',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\masked\\test_function.py',
   'PYMODULE'),
  ('pandas.tests.arrays.masked.test_indexing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\masked\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.masked_shared',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\masked_shared.py',
   'PYMODULE'),
  ('pandas.tests.arrays.numpy_',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\numpy_\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.numpy_.test_indexing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\numpy_\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.numpy_.test_numpy',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\numpy_\\test_numpy.py',
   'PYMODULE'),
  ('pandas.tests.arrays.period',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\period\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.period.test_arrow_compat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\period\\test_arrow_compat.py',
   'PYMODULE'),
  ('pandas.tests.arrays.period.test_astype',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\period\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.period.test_constructors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\period\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.arrays.period.test_reductions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\period\\test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_accessor',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_accessor.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_arithmetics',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_arithmetics.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_array',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_array.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_astype',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_combine_concat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_combine_concat.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_constructors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_dtype',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_dtype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_indexing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_libsparse',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_libsparse.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_reductions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_unary',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_unary.py',
   'PYMODULE'),
  ('pandas.tests.arrays.string_',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\string_\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.string_.test_string',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\string_\\test_string.py',
   'PYMODULE'),
  ('pandas.tests.arrays.string_.test_string_arrow',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\string_\\test_string_arrow.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_array',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\test_array.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_datetimelike',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\test_datetimelike.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_datetimes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\test_datetimes.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_ndarray_backed',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\test_ndarray_backed.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_period',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\test_period.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_timedeltas',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\test_timedeltas.py',
   'PYMODULE'),
  ('pandas.tests.arrays.timedeltas',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\timedeltas\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.timedeltas.test_constructors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\timedeltas\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.arrays.timedeltas.test_cumulative',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\timedeltas\\test_cumulative.py',
   'PYMODULE'),
  ('pandas.tests.arrays.timedeltas.test_reductions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\arrays\\timedeltas\\test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.base',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\base\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.base.common',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\base\\common.py',
   'PYMODULE'),
  ('pandas.tests.base.test_constructors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\base\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.base.test_conversion',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\base\\test_conversion.py',
   'PYMODULE'),
  ('pandas.tests.base.test_fillna',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\base\\test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.base.test_misc',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\base\\test_misc.py',
   'PYMODULE'),
  ('pandas.tests.base.test_transpose',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\base\\test_transpose.py',
   'PYMODULE'),
  ('pandas.tests.base.test_unique',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\base\\test_unique.py',
   'PYMODULE'),
  ('pandas.tests.base.test_value_counts',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\base\\test_value_counts.py',
   'PYMODULE'),
  ('pandas.tests.computation',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.computation.test_compat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\computation\\test_compat.py',
   'PYMODULE'),
  ('pandas.tests.computation.test_eval',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\computation\\test_eval.py',
   'PYMODULE'),
  ('pandas.tests.config',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\config\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.config.test_config',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\config\\test_config.py',
   'PYMODULE'),
  ('pandas.tests.config.test_localization',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\config\\test_localization.py',
   'PYMODULE'),
  ('pandas.tests.construction',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\construction\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.construction.test_extract_array',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\construction\\test_extract_array.py',
   'PYMODULE'),
  ('pandas.tests.copy_view',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\copy_view\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.index',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\copy_view\\index\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.index.test_datetimeindex',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\copy_view\\index\\test_datetimeindex.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.index.test_index',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\copy_view\\index\\test_index.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.index.test_periodindex',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\copy_view\\index\\test_periodindex.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.index.test_timedeltaindex',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\copy_view\\index\\test_timedeltaindex.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_array',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_array.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_astype',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_chained_assignment_deprecation',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_chained_assignment_deprecation.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_clip',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_clip.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_constructors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_core_functionalities',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_core_functionalities.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_functions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_functions.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_indexing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_internals',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_internals.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_interp_fillna',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_interp_fillna.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_methods',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_methods.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_replace',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_replace.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_setitem',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_setitem.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_util',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_util.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.util',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\copy_view\\util.py',
   'PYMODULE'),
  ('pandas.tests.dtypes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_can_hold_element',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_can_hold_element.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_construct_from_scalar',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_construct_from_scalar.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_construct_ndarray',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_construct_ndarray.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_construct_object_arr',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_construct_object_arr.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_dict_compat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_dict_compat.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_downcast',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_downcast.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_find_common_type',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_find_common_type.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_infer_datetimelike',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_infer_datetimelike.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_infer_dtype',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_infer_dtype.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_maybe_box_native',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_maybe_box_native.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_promote',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_promote.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.test_common',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\dtypes\\test_common.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.test_concat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\dtypes\\test_concat.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.test_dtypes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\dtypes\\test_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.test_generic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\dtypes\\test_generic.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.test_inference',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\dtypes\\test_inference.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.test_missing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\dtypes\\test_missing.py',
   'PYMODULE'),
  ('pandas.tests.extension',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.array_with_attr',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\array_with_attr\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.array_with_attr.array',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\array_with_attr\\array.py',
   'PYMODULE'),
  ('pandas.tests.extension.array_with_attr.test_array_with_attr',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\array_with_attr\\test_array_with_attr.py',
   'PYMODULE'),
  ('pandas.tests.extension.base',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.accumulate',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\accumulate.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.casting',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\casting.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.constructors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\constructors.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.dim2',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\dim2.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.dtype',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\dtype.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.getitem',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\getitem.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.groupby',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\groupby.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.index',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\index.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.interface',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\interface.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.io',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\io.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.methods',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\methods.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.missing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\missing.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.ops',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\ops.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.printing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\printing.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.reduce',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\reduce.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.reshaping',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\reshaping.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.setitem',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\setitem.py',
   'PYMODULE'),
  ('pandas.tests.extension.conftest',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.extension.date',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\date\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.date.array',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\date\\array.py',
   'PYMODULE'),
  ('pandas.tests.extension.decimal',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\decimal\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.decimal.array',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\decimal\\array.py',
   'PYMODULE'),
  ('pandas.tests.extension.decimal.test_decimal',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\decimal\\test_decimal.py',
   'PYMODULE'),
  ('pandas.tests.extension.json',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.json.array',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\json\\array.py',
   'PYMODULE'),
  ('pandas.tests.extension.json.test_json',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\json\\test_json.py',
   'PYMODULE'),
  ('pandas.tests.extension.list',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\list\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.list.array',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\list\\array.py',
   'PYMODULE'),
  ('pandas.tests.extension.list.test_list',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\list\\test_list.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_arrow',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\test_arrow.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_categorical',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_common',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\test_common.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_datetime',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\test_datetime.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_extension',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\test_extension.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_interval',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\test_interval.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_masked',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\test_masked.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_numpy',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\test_numpy.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_period',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\test_period.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_sparse',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\test_sparse.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_string',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\extension\\test_string.py',
   'PYMODULE'),
  ('pandas.tests.frame',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.frame.common',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\common.py',
   'PYMODULE'),
  ('pandas.tests.frame.conftest',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.frame.constructors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\constructors\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.frame.constructors.test_from_dict',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\constructors\\test_from_dict.py',
   'PYMODULE'),
  ('pandas.tests.frame.constructors.test_from_records',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\constructors\\test_from_records.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_coercion',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_coercion.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_delitem',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_delitem.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_get',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_get.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_get_value',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_get_value.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_getitem',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_getitem.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_indexing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_insert',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_insert.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_mask',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_mask.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_set_value',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_set_value.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_setitem',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_setitem.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_take',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_take.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_where',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_where.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_xs',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_xs.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_add_prefix_suffix',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_add_prefix_suffix.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_align',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_align.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_asfreq',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_asfreq.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_asof',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_asof.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_assign',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_assign.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_astype',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_at_time',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_at_time.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_between_time',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_between_time.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_clip',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_clip.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_combine',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_combine.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_combine_first',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_combine_first.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_compare',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_compare.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_convert_dtypes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_convert_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_copy',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_copy.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_count',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_count.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_cov_corr',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_cov_corr.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_describe',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_describe.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_diff',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_diff.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_dot',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_dot.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_drop',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_drop.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_drop_duplicates',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_drop_duplicates.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_droplevel',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_droplevel.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_dropna',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_dropna.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_dtypes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_duplicated',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_duplicated.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_equals',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_equals.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_explode',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_explode.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_fillna',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_filter',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_filter.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_first_and_last',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_first_and_last.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_first_valid_index',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_first_valid_index.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_get_numeric_data',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_get_numeric_data.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_head_tail',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_head_tail.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_infer_objects',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_infer_objects.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_info',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_info.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_interpolate',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_interpolate.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_is_homogeneous_dtype',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_is_homogeneous_dtype.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_isetitem',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_isetitem.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_isin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_isin.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_iterrows',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_iterrows.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_join',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_map',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_map.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_matmul',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_matmul.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_nlargest',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_nlargest.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_pct_change',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_pct_change.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_pipe',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_pipe.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_pop',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_pop.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_quantile',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_quantile.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_rank',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_rank.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_reindex',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_reindex.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_reindex_like',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_reindex_like.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_rename',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_rename.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_rename_axis',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_rename_axis.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_reorder_levels',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_reorder_levels.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_replace',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_replace.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_reset_index',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_reset_index.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_round',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_round.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_sample',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_sample.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_select_dtypes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_select_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_set_axis',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_set_axis.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_set_index',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_set_index.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_shift',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_shift.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_size',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_size.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_sort_index',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_sort_index.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_sort_values',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_sort_values.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_swapaxes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_swapaxes.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_swaplevel',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_swaplevel.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_csv',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_csv.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_dict',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_dict.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_dict_of_blocks',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_dict_of_blocks.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_numpy',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_numpy.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_period',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_period.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_records',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_records.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_timestamp',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_timestamp.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_transpose',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_transpose.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_truncate',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_truncate.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_tz_convert',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_tz_convert.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_tz_localize',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_tz_localize.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_update',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_update.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_value_counts',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_value_counts.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_values',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_values.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_alter_axes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\test_alter_axes.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_api',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_arithmetic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_arrow_interface',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\test_arrow_interface.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_block_internals',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\test_block_internals.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_constructors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_cumulative',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\test_cumulative.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_iteration',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\test_iteration.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_logical_ops',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\test_logical_ops.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_nonunique_indexes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\test_nonunique_indexes.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_npfuncs',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\test_npfuncs.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_query_eval',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\test_query_eval.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_reductions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_repr',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\test_repr.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_stack_unstack',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\test_stack_unstack.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_subclass',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\test_subclass.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_ufunc',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\test_ufunc.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_unary',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\test_unary.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_validate',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\frame\\test_validate.py',
   'PYMODULE'),
  ('pandas.tests.generic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\generic\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_duplicate_labels',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\generic\\test_duplicate_labels.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_finalize',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\generic\\test_finalize.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_frame',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\generic\\test_frame.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_generic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\generic\\test_generic.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_label_or_level_utils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\generic\\test_label_or_level_utils.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_series',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\generic\\test_series.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_to_xarray',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\generic\\test_to_xarray.py',
   'PYMODULE'),
  ('pandas.tests.groupby',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.groupby.aggregate',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\aggregate\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.groupby.aggregate.test_aggregate',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\aggregate\\test_aggregate.py',
   'PYMODULE'),
  ('pandas.tests.groupby.aggregate.test_cython',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\aggregate\\test_cython.py',
   'PYMODULE'),
  ('pandas.tests.groupby.aggregate.test_numba',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\aggregate\\test_numba.py',
   'PYMODULE'),
  ('pandas.tests.groupby.aggregate.test_other',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\aggregate\\test_other.py',
   'PYMODULE'),
  ('pandas.tests.groupby.conftest',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_corrwith',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_corrwith.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_describe',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_describe.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_groupby_shift_diff',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_groupby_shift_diff.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_is_monotonic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_is_monotonic.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_nlargest_nsmallest',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_nlargest_nsmallest.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_nth',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_nth.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_quantile',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_quantile.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_rank',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_rank.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_sample',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_sample.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_size',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_size.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_skew',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_skew.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_value_counts',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_value_counts.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_all_methods',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_all_methods.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_api',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_apply',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_apply.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_apply_mutate',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_apply_mutate.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_bin_groupby',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_bin_groupby.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_categorical',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_counting',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_counting.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_cumulative',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_cumulative.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_filters',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_filters.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_groupby',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_groupby.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_groupby_dropna',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_groupby_dropna.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_groupby_subclass',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_groupby_subclass.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_grouping',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_grouping.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_index_as_string',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_index_as_string.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_indexing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_libgroupby',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_libgroupby.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_missing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_missing.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_numba',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_numba.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_numeric_only',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_numeric_only.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_pipe',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_pipe.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_raises',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_raises.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_reductions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_timegrouper',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_timegrouper.py',
   'PYMODULE'),
  ('pandas.tests.groupby.transform',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\transform\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.groupby.transform.test_numba',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\transform\\test_numba.py',
   'PYMODULE'),
  ('pandas.tests.groupby.transform.test_transform',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\groupby\\transform\\test_transform.py',
   'PYMODULE'),
  ('pandas.tests.indexes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_constructors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_formats',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_indexing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_pickle',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_pickle.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_reshape',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_reshape.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_setops',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_where',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_where.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_append',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_append.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_astype',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_category',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_category.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_constructors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_equals',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_equals.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_fillna',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_formats',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_indexing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_map',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_map.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_reindex',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_reindex.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_setops',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.conftest',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_drop_duplicates',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_drop_duplicates.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_equals',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_equals.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_indexing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_is_monotonic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_is_monotonic.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_nat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_nat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_sort_values',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_sort_values.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_value_counts',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_value_counts.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_asof',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_asof.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_astype',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_delete',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_delete.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_factorize',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_factorize.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_fillna',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_insert',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_insert.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_isocalendar',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_isocalendar.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_map',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_map.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_normalize',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_normalize.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_repeat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_repeat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_resolution',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_resolution.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_round',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_round.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_shift',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_shift.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_snap',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_snap.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_to_frame',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_to_frame.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_to_julian_date',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_to_julian_date.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_to_period',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_to_period.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_to_pydatetime',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_to_pydatetime.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_to_series',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_to_series.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_tz_convert',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_tz_convert.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_tz_localize',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_tz_localize.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_unique',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_unique.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_arithmetic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_constructors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_date_range',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_date_range.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_datetime',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_datetime.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_formats',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_freq_attr',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_freq_attr.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_indexing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_iter',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_iter.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_join',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_npfuncs',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_npfuncs.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_ops',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_ops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_partial_slicing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_partial_slicing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_pickle',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_pickle.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_reindex',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_reindex.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_scalar_compat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_scalar_compat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_setops',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_timezones',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_timezones.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_astype',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_constructors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_equals',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_equals.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_formats',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_indexing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_interval',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_interval.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_interval_range',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_interval_range.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_interval_tree',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_interval_tree.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_join',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_pickle',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_pickle.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_setops',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.conftest',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_analytics',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_analytics.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_astype',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_compat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_compat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_constructors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_conversion',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_conversion.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_copy',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_copy.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_drop',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_drop.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_duplicates',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_duplicates.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_equivalence',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_equivalence.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_formats',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_get_level_values',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_get_level_values.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_get_set',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_get_set.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_indexing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_integrity',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_integrity.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_isin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_isin.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_join',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_lexsort',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_lexsort.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_missing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_missing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_monotonic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_monotonic.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_names',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_names.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_partial_indexing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_partial_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_pickle',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_pickle.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_reindex',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_reindex.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_reshape',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_reshape.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_setops',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_sorting',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_sorting.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_take',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_take.py',
   'PYMODULE'),
  ('pandas.tests.indexes.numeric',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\numeric\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.numeric.test_astype',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\numeric\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.numeric.test_indexing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\numeric\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.numeric.test_join',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\numeric\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.indexes.numeric.test_numeric',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\numeric\\test_numeric.py',
   'PYMODULE'),
  ('pandas.tests.indexes.numeric.test_setops',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\numeric\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.object',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\object\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.object.test_astype',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\object\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.object.test_indexing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\object\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_asfreq',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_asfreq.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_astype',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_factorize',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_factorize.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_fillna',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_insert',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_insert.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_is_full',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_is_full.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_repeat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_repeat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_shift',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_shift.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_to_timestamp',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_to_timestamp.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_constructors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_formats',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_freq_attr',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_freq_attr.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_indexing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_join',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_monotonic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_monotonic.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_partial_slicing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_partial_slicing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_period',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_period.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_period_range',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_period_range.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_pickle',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_pickle.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_resolution',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_resolution.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_scalar_compat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_scalar_compat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_searchsorted',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_searchsorted.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_setops',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_tools',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_tools.py',
   'PYMODULE'),
  ('pandas.tests.indexes.ranges',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\ranges\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.ranges.test_constructors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\ranges\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.ranges.test_indexing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\ranges\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.ranges.test_join',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\ranges\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.indexes.ranges.test_range',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\ranges\\test_range.py',
   'PYMODULE'),
  ('pandas.tests.indexes.ranges.test_setops',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\ranges\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_any_index',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\test_any_index.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_base',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\test_base.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_common',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\test_common.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_datetimelike',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\test_datetimelike.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_engines',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\test_engines.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_frozen',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\test_frozen.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_index_new',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\test_index_new.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_indexing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_numpy_compat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\test_numpy_compat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_old_base',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\test_old_base.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_setops',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_subclass',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\test_subclass.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods.test_astype',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods.test_factorize',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\test_factorize.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods.test_fillna',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods.test_insert',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\test_insert.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods.test_repeat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\test_repeat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods.test_shift',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\test_shift.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_arithmetic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_constructors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_delete',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_delete.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_formats',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_freq_attr',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_freq_attr.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_indexing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_join',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_ops',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_ops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_pickle',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_pickle.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_scalar_compat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_scalar_compat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_searchsorted',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_searchsorted.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_setops',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_timedelta',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_timedelta.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_timedelta_range',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_timedelta_range.py',
   'PYMODULE'),
  ('pandas.tests.indexing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexing.common',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\common.py',
   'PYMODULE'),
  ('pandas.tests.indexing.conftest',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.indexing.interval',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\interval\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexing.interval.test_interval',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\interval\\test_interval.py',
   'PYMODULE'),
  ('pandas.tests.indexing.interval.test_interval_new',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\interval\\test_interval_new.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_chaining_and_caching',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_chaining_and_caching.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_datetime',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_datetime.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_getitem',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_getitem.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_iloc',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_iloc.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_indexing_slow',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_indexing_slow.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_loc',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_loc.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_multiindex',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_multiindex.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_partial',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_partial.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_setitem',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_setitem.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_slice',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_slice.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_sorted',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_sorted.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_at',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\test_at.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_categorical',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_chaining_and_caching',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\test_chaining_and_caching.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_check_indexer',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\test_check_indexer.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_coercion',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\test_coercion.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_datetime',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\test_datetime.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_floats',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\test_floats.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_iat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\test_iat.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_iloc',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\test_iloc.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_indexers',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\test_indexers.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_indexing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_loc',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\test_loc.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_na_indexing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\test_na_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_partial',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\test_partial.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_scalar',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\indexing\\test_scalar.py',
   'PYMODULE'),
  ('pandas.tests.interchange',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.interchange.test_impl',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\interchange\\test_impl.py',
   'PYMODULE'),
  ('pandas.tests.interchange.test_spec_conformance',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\interchange\\test_spec_conformance.py',
   'PYMODULE'),
  ('pandas.tests.interchange.test_utils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\interchange\\test_utils.py',
   'PYMODULE'),
  ('pandas.tests.internals',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.internals.test_api',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\internals\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.internals.test_internals',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\internals\\test_internals.py',
   'PYMODULE'),
  ('pandas.tests.internals.test_managers',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\internals\\test_managers.py',
   'PYMODULE'),
  ('pandas.tests.io',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.conftest',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.io.excel',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_odf',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_odf.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_odswriter',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_odswriter.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_openpyxl',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_openpyxl.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_readers',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_readers.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_style',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_style.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_writers',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_writers.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_xlrd',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_xlrd.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_xlsxwriter',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.tests.io.formats',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_bar',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_bar.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_exceptions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_exceptions.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_format',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_format.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_highlight',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_highlight.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_html',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_html.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_matplotlib',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_matplotlib.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_non_unique',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_non_unique.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_style',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_style.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_to_latex',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_to_latex.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_to_string',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_to_string.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_tooltip',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_tooltip.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_console',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_console.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_css',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_css.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_eng_formatting',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_eng_formatting.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_format',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_format.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_ipython_compat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_ipython_compat.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_printing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_printing.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_to_csv',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_to_csv.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_to_excel',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_to_excel.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_to_html',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_to_html.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_to_latex',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_to_latex.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_to_markdown',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_to_markdown.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_to_string',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_to_string.py',
   'PYMODULE'),
  ('pandas.tests.io.generate_legacy_storage_files',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\generate_legacy_storage_files.py',
   'PYMODULE'),
  ('pandas.tests.io.json',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.json.conftest',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\json\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_compression',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_compression.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_deprecated_kwargs',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_deprecated_kwargs.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_json_table_schema',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_json_table_schema.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_json_table_schema_ext_dtype',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_json_table_schema_ext_dtype.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_normalize',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_normalize.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_pandas',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_pandas.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_readlines',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_readlines.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_ujson',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_ujson.py',
   'PYMODULE'),
  ('pandas.tests.io.parser',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_chunksize',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_chunksize.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_common_basic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_common_basic.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_data_list',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_data_list.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_decimal',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_decimal.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_file_buffer_url',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_file_buffer_url.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_float',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_float.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_index',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_index.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_inf',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_inf.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_ints',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_ints.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_iterator',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_iterator.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_read_errors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_read_errors.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_verbose',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_verbose.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.conftest',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.dtypes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.dtypes.test_categorical',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\dtypes\\test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.dtypes.test_dtypes_basic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\dtypes\\test_dtypes_basic.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.dtypes.test_empty',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\dtypes\\test_empty.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_c_parser_only',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_c_parser_only.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_comment',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_comment.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_compression',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_compression.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_concatenate_chunks',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_concatenate_chunks.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_converters',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_converters.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_dialect',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_dialect.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_encoding',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_encoding.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_header',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_header.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_index_col',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_index_col.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_mangle_dupes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_mangle_dupes.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_multi_thread',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_multi_thread.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_na_values',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_na_values.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_network',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_network.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_parse_dates',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_parse_dates.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_python_parser_only',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_python_parser_only.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_quoting',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_quoting.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_read_fwf',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_read_fwf.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_skiprows',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_skiprows.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_textreader',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_textreader.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_unsupported',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_unsupported.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_upcast',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_upcast.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.usecols',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\usecols\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.usecols.test_parse_dates',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\usecols\\test_parse_dates.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.usecols.test_strings',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\usecols\\test_strings.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.usecols.test_usecols_basic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\usecols\\test_usecols_basic.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.common',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\common.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.conftest',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_append',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_append.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_categorical',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_compat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_compat.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_complex',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_complex.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_errors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_errors.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_file_handling',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_file_handling.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_keys',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_keys.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_put',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_put.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_pytables_missing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_pytables_missing.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_read',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_read.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_retain_attributes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_retain_attributes.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_round_trip',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_round_trip.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_select',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_select.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_store',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_store.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_subclass',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_subclass.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_time_series',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_time_series.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_timezones',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_timezones.py',
   'PYMODULE'),
  ('pandas.tests.io.sas',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.sas.test_byteswap',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\sas\\test_byteswap.py',
   'PYMODULE'),
  ('pandas.tests.io.sas.test_sas',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\sas\\test_sas.py',
   'PYMODULE'),
  ('pandas.tests.io.sas.test_sas7bdat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\sas\\test_sas7bdat.py',
   'PYMODULE'),
  ('pandas.tests.io.sas.test_xport',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\sas\\test_xport.py',
   'PYMODULE'),
  ('pandas.tests.io.test_clipboard',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\test_clipboard.py',
   'PYMODULE'),
  ('pandas.tests.io.test_common',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\test_common.py',
   'PYMODULE'),
  ('pandas.tests.io.test_compression',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\test_compression.py',
   'PYMODULE'),
  ('pandas.tests.io.test_feather',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\test_feather.py',
   'PYMODULE'),
  ('pandas.tests.io.test_fsspec',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\test_fsspec.py',
   'PYMODULE'),
  ('pandas.tests.io.test_gbq',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\test_gbq.py',
   'PYMODULE'),
  ('pandas.tests.io.test_gcs',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\test_gcs.py',
   'PYMODULE'),
  ('pandas.tests.io.test_html',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\test_html.py',
   'PYMODULE'),
  ('pandas.tests.io.test_http_headers',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\test_http_headers.py',
   'PYMODULE'),
  ('pandas.tests.io.test_orc',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\test_orc.py',
   'PYMODULE'),
  ('pandas.tests.io.test_parquet',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\test_parquet.py',
   'PYMODULE'),
  ('pandas.tests.io.test_pickle',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\test_pickle.py',
   'PYMODULE'),
  ('pandas.tests.io.test_s3',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\test_s3.py',
   'PYMODULE'),
  ('pandas.tests.io.test_spss',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\test_spss.py',
   'PYMODULE'),
  ('pandas.tests.io.test_sql',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\test_sql.py',
   'PYMODULE'),
  ('pandas.tests.io.test_stata',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\test_stata.py',
   'PYMODULE'),
  ('pandas.tests.io.xml',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\xml\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.xml.conftest',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\xml\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.io.xml.test_to_xml',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\xml\\test_to_xml.py',
   'PYMODULE'),
  ('pandas.tests.io.xml.test_xml',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\xml\\test_xml.py',
   'PYMODULE'),
  ('pandas.tests.io.xml.test_xml_dtypes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\io\\xml\\test_xml_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.libs',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\libs\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.libs.test_hashtable',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\libs\\test_hashtable.py',
   'PYMODULE'),
  ('pandas.tests.libs.test_join',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\libs\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.libs.test_lib',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\libs\\test_lib.py',
   'PYMODULE'),
  ('pandas.tests.libs.test_libalgos',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\libs\\test_libalgos.py',
   'PYMODULE'),
  ('pandas.tests.plotting',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.plotting.common',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\plotting\\common.py',
   'PYMODULE'),
  ('pandas.tests.plotting.conftest',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\plotting\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\plotting\\frame\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame.test_frame',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\plotting\\frame\\test_frame.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame.test_frame_color',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\plotting\\frame\\test_frame_color.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame.test_frame_groupby',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\plotting\\frame\\test_frame_groupby.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame.test_frame_legend',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\plotting\\frame\\test_frame_legend.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame.test_frame_subplots',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\plotting\\frame\\test_frame_subplots.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame.test_hist_box_by',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\plotting\\frame\\test_hist_box_by.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_backend',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\plotting\\test_backend.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_boxplot_method',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\plotting\\test_boxplot_method.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_common',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\plotting\\test_common.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_converter',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\plotting\\test_converter.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_datetimelike',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\plotting\\test_datetimelike.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_groupby',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\plotting\\test_groupby.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_hist_method',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\plotting\\test_hist_method.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_misc',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\plotting\\test_misc.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_series',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\plotting\\test_series.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_style',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\plotting\\test_style.py',
   'PYMODULE'),
  ('pandas.tests.reductions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reductions\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.reductions.test_reductions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reductions\\test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.reductions.test_stat_reductions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reductions\\test_stat_reductions.py',
   'PYMODULE'),
  ('pandas.tests.resample',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\resample\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.resample.conftest',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\resample\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_base',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\resample\\test_base.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_datetime_index',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\resample\\test_datetime_index.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_period_index',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\resample\\test_period_index.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_resample_api',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\resample\\test_resample_api.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_resampler_grouper',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\resample\\test_resampler_grouper.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_time_grouper',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\resample\\test_time_grouper.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_timedelta',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\resample\\test_timedelta.py',
   'PYMODULE'),
  ('pandas.tests.reshape',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.conftest',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_append',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_append.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_append_common',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_append_common.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_categorical',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_concat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_concat.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_dataframe',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_dataframe.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_datetimes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_datetimes.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_empty',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_empty.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_index',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_index.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_invalid',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_invalid.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_series',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_series.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_sort',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_sort.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_join',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_merge',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_merge.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_merge_asof',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_merge_asof.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_merge_cross',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_merge_cross.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_merge_index_as_string',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_merge_index_as_string.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_merge_ordered',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_merge_ordered.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_multi',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_multi.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_crosstab',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\test_crosstab.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_cut',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\test_cut.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_from_dummies',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\test_from_dummies.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_get_dummies',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\test_get_dummies.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_melt',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\test_melt.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_pivot',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\test_pivot.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_pivot_multilevel',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\test_pivot_multilevel.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_qcut',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\test_qcut.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_union_categoricals',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\test_union_categoricals.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_util',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\reshape\\test_util.py',
   'PYMODULE'),
  ('pandas.tests.scalar',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\interval\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval.test_arithmetic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\interval\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval.test_constructors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\interval\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval.test_contains',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\interval\\test_contains.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval.test_formats',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\interval\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval.test_interval',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\interval\\test_interval.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval.test_overlaps',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\interval\\test_overlaps.py',
   'PYMODULE'),
  ('pandas.tests.scalar.period',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\period\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.scalar.period.test_arithmetic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\period\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.scalar.period.test_asfreq',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\period\\test_asfreq.py',
   'PYMODULE'),
  ('pandas.tests.scalar.period.test_period',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\period\\test_period.py',
   'PYMODULE'),
  ('pandas.tests.scalar.test_na_scalar',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\test_na_scalar.py',
   'PYMODULE'),
  ('pandas.tests.scalar.test_nat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\test_nat.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.methods',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.methods.test_as_unit',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\methods\\test_as_unit.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.methods.test_round',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\methods\\test_round.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.test_arithmetic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.test_constructors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.test_formats',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.test_timedelta',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\test_timedelta.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_as_unit',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_as_unit.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_normalize',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_normalize.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_replace',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_replace.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_round',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_round.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_timestamp_method',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_timestamp_method.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_to_julian_date',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_to_julian_date.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_to_pydatetime',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_to_pydatetime.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_tz_convert',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_tz_convert.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_tz_localize',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_tz_localize.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_arithmetic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_comparisons',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_comparisons.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_constructors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_formats',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_timestamp',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_timestamp.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_timezones',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_timezones.py',
   'PYMODULE'),
  ('pandas.tests.series',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\accessors\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors.test_cat_accessor',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\accessors\\test_cat_accessor.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors.test_dt_accessor',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\accessors\\test_dt_accessor.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors.test_list_accessor',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\accessors\\test_list_accessor.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors.test_sparse_accessor',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\accessors\\test_sparse_accessor.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors.test_str_accessor',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\accessors\\test_str_accessor.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors.test_struct_accessor',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\accessors\\test_struct_accessor.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_datetime',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_datetime.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_delitem',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_delitem.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_get',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_get.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_getitem',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_getitem.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_indexing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_mask',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_mask.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_set_value',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_set_value.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_setitem',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_setitem.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_take',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_take.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_where',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_where.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_xs',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_xs.py',
   'PYMODULE'),
  ('pandas.tests.series.methods',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_add_prefix_suffix',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_add_prefix_suffix.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_align',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_align.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_argsort',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_argsort.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_asof',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_asof.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_astype',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_autocorr',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_autocorr.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_between',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_between.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_case_when',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_case_when.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_clip',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_clip.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_combine',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_combine.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_combine_first',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_combine_first.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_compare',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_compare.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_convert_dtypes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_convert_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_copy',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_copy.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_count',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_count.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_cov_corr',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_cov_corr.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_describe',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_describe.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_diff',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_diff.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_drop',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_drop.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_drop_duplicates',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_drop_duplicates.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_dropna',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_dropna.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_dtypes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_duplicated',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_duplicated.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_equals',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_equals.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_explode',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_explode.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_fillna',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_get_numeric_data',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_get_numeric_data.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_head_tail',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_head_tail.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_infer_objects',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_infer_objects.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_info',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_info.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_interpolate',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_interpolate.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_is_monotonic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_is_monotonic.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_is_unique',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_is_unique.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_isin',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_isin.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_isna',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_isna.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_item',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_item.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_map',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_map.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_matmul',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_matmul.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_nlargest',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_nlargest.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_nunique',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_nunique.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_pct_change',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_pct_change.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_pop',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_pop.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_quantile',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_quantile.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_rank',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_rank.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_reindex',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_reindex.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_reindex_like',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_reindex_like.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_rename',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_rename.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_rename_axis',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_rename_axis.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_repeat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_repeat.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_replace',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_replace.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_reset_index',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_reset_index.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_round',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_round.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_searchsorted',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_searchsorted.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_set_name',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_set_name.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_size',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_size.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_sort_index',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_sort_index.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_sort_values',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_sort_values.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_to_csv',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_to_csv.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_to_dict',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_to_dict.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_to_frame',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_to_frame.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_to_numpy',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_to_numpy.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_tolist',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_tolist.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_truncate',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_truncate.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_tz_localize',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_tz_localize.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_unique',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_unique.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_unstack',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_unstack.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_update',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_update.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_value_counts',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_value_counts.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_values',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_values.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_view',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_view.py',
   'PYMODULE'),
  ('pandas.tests.series.test_api',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.series.test_arithmetic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.series.test_constructors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.series.test_cumulative',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\test_cumulative.py',
   'PYMODULE'),
  ('pandas.tests.series.test_formats',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.series.test_iteration',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\test_iteration.py',
   'PYMODULE'),
  ('pandas.tests.series.test_logical_ops',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\test_logical_ops.py',
   'PYMODULE'),
  ('pandas.tests.series.test_missing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\test_missing.py',
   'PYMODULE'),
  ('pandas.tests.series.test_npfuncs',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\test_npfuncs.py',
   'PYMODULE'),
  ('pandas.tests.series.test_reductions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.series.test_subclass',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\test_subclass.py',
   'PYMODULE'),
  ('pandas.tests.series.test_ufunc',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\test_ufunc.py',
   'PYMODULE'),
  ('pandas.tests.series.test_unary',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\test_unary.py',
   'PYMODULE'),
  ('pandas.tests.series.test_validate',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\series\\test_validate.py',
   'PYMODULE'),
  ('pandas.tests.strings',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.strings.conftest',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\strings\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_api',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\strings\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_case_justify',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\strings\\test_case_justify.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_cat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\strings\\test_cat.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_extract',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\strings\\test_extract.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_find_replace',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\strings\\test_find_replace.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_get_dummies',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\strings\\test_get_dummies.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_split_partition',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\strings\\test_split_partition.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_string_array',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\strings\\test_string_array.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_strings',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\strings\\test_strings.py',
   'PYMODULE'),
  ('pandas.tests.test_aggregation',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\test_aggregation.py',
   'PYMODULE'),
  ('pandas.tests.test_algos',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\test_algos.py',
   'PYMODULE'),
  ('pandas.tests.test_common',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\test_common.py',
   'PYMODULE'),
  ('pandas.tests.test_downstream',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\test_downstream.py',
   'PYMODULE'),
  ('pandas.tests.test_errors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\test_errors.py',
   'PYMODULE'),
  ('pandas.tests.test_expressions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\test_expressions.py',
   'PYMODULE'),
  ('pandas.tests.test_flags',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\test_flags.py',
   'PYMODULE'),
  ('pandas.tests.test_multilevel',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\test_multilevel.py',
   'PYMODULE'),
  ('pandas.tests.test_nanops',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\test_nanops.py',
   'PYMODULE'),
  ('pandas.tests.test_optional_dependency',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\test_optional_dependency.py',
   'PYMODULE'),
  ('pandas.tests.test_register_accessor',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\test_register_accessor.py',
   'PYMODULE'),
  ('pandas.tests.test_sorting',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\test_sorting.py',
   'PYMODULE'),
  ('pandas.tests.test_take',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\test_take.py',
   'PYMODULE'),
  ('pandas.tests.tools',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.tools.test_to_datetime',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tools\\test_to_datetime.py',
   'PYMODULE'),
  ('pandas.tests.tools.test_to_numeric',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tools\\test_to_numeric.py',
   'PYMODULE'),
  ('pandas.tests.tools.test_to_time',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tools\\test_to_time.py',
   'PYMODULE'),
  ('pandas.tests.tools.test_to_timedelta',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tools\\test_to_timedelta.py',
   'PYMODULE'),
  ('pandas.tests.tseries',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.tseries.frequencies',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\frequencies\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.tseries.frequencies.test_freq_code',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\frequencies\\test_freq_code.py',
   'PYMODULE'),
  ('pandas.tests.tseries.frequencies.test_frequencies',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\frequencies\\test_frequencies.py',
   'PYMODULE'),
  ('pandas.tests.tseries.frequencies.test_inference',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\frequencies\\test_inference.py',
   'PYMODULE'),
  ('pandas.tests.tseries.holiday',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\holiday\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.tseries.holiday.test_calendar',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\holiday\\test_calendar.py',
   'PYMODULE'),
  ('pandas.tests.tseries.holiday.test_federal',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\holiday\\test_federal.py',
   'PYMODULE'),
  ('pandas.tests.tseries.holiday.test_holiday',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\holiday\\test_holiday.py',
   'PYMODULE'),
  ('pandas.tests.tseries.holiday.test_observance',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\holiday\\test_observance.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.common',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\common.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_business_day',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_business_day.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_business_hour',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_business_hour.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_business_month',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_business_month.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_business_quarter',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_business_quarter.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_business_year',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_business_year.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_common',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_common.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_custom_business_day',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_custom_business_day.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_custom_business_hour',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_custom_business_hour.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_custom_business_month',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_custom_business_month.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_dst',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_dst.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_easter',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_easter.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_fiscal',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_fiscal.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_index',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_index.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_month',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_month.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_offsets',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_offsets.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_offsets_properties',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_offsets_properties.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_quarter',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_quarter.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_ticks',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_ticks.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_week',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_week.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_year',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_year.py',
   'PYMODULE'),
  ('pandas.tests.tslibs',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_api',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_array_to_datetime',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_array_to_datetime.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_ccalendar',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_ccalendar.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_conversion',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_conversion.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_fields',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_fields.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_libfrequencies',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_libfrequencies.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_liboffsets',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_liboffsets.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_np_datetime',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_np_datetime.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_npy_units',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_npy_units.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_parse_iso8601',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_parse_iso8601.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_parsing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_parsing.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_period',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_period.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_resolution',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_resolution.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_strptime',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_strptime.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_timedeltas',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_timedeltas.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_timezones',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_timezones.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_to_offset',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_to_offset.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_tzconversion',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_tzconversion.py',
   'PYMODULE'),
  ('pandas.tests.util',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.util.conftest',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\util\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_almost_equal',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_almost_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_attr_equal',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_attr_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_categorical_equal',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_categorical_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_extension_array_equal',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_extension_array_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_frame_equal',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_frame_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_index_equal',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_index_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_interval_array_equal',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_interval_array_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_numpy_array_equal',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_numpy_array_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_produces_warning',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_produces_warning.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_series_equal',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_series_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_deprecate',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\util\\test_deprecate.py',
   'PYMODULE'),
  ('pandas.tests.util.test_deprecate_kwarg',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\util\\test_deprecate_kwarg.py',
   'PYMODULE'),
  ('pandas.tests.util.test_deprecate_nonkeyword_arguments',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\util\\test_deprecate_nonkeyword_arguments.py',
   'PYMODULE'),
  ('pandas.tests.util.test_doc',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\util\\test_doc.py',
   'PYMODULE'),
  ('pandas.tests.util.test_hashing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\util\\test_hashing.py',
   'PYMODULE'),
  ('pandas.tests.util.test_numba',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\util\\test_numba.py',
   'PYMODULE'),
  ('pandas.tests.util.test_rewrite_warning',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\util\\test_rewrite_warning.py',
   'PYMODULE'),
  ('pandas.tests.util.test_shares_memory',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\util\\test_shares_memory.py',
   'PYMODULE'),
  ('pandas.tests.util.test_show_versions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\util\\test_show_versions.py',
   'PYMODULE'),
  ('pandas.tests.util.test_util',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\util\\test_util.py',
   'PYMODULE'),
  ('pandas.tests.util.test_validate_args',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\util\\test_validate_args.py',
   'PYMODULE'),
  ('pandas.tests.util.test_validate_args_and_kwargs',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\util\\test_validate_args_and_kwargs.py',
   'PYMODULE'),
  ('pandas.tests.util.test_validate_inclusive',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\util\\test_validate_inclusive.py',
   'PYMODULE'),
  ('pandas.tests.util.test_validate_kwargs',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\util\\test_validate_kwargs.py',
   'PYMODULE'),
  ('pandas.tests.window',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.window.conftest',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\window\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.window.moments',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\window\\moments\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.window.moments.conftest',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\window\\moments\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.window.moments.test_moments_consistency_ewm',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\window\\moments\\test_moments_consistency_ewm.py',
   'PYMODULE'),
  ('pandas.tests.window.moments.test_moments_consistency_expanding',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\window\\moments\\test_moments_consistency_expanding.py',
   'PYMODULE'),
  ('pandas.tests.window.moments.test_moments_consistency_rolling',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\window\\moments\\test_moments_consistency_rolling.py',
   'PYMODULE'),
  ('pandas.tests.window.test_api',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\window\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.window.test_apply',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\window\\test_apply.py',
   'PYMODULE'),
  ('pandas.tests.window.test_base_indexer',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\window\\test_base_indexer.py',
   'PYMODULE'),
  ('pandas.tests.window.test_cython_aggregations',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\window\\test_cython_aggregations.py',
   'PYMODULE'),
  ('pandas.tests.window.test_dtypes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\window\\test_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.window.test_ewm',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\window\\test_ewm.py',
   'PYMODULE'),
  ('pandas.tests.window.test_expanding',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\window\\test_expanding.py',
   'PYMODULE'),
  ('pandas.tests.window.test_groupby',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\window\\test_groupby.py',
   'PYMODULE'),
  ('pandas.tests.window.test_numba',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\window\\test_numba.py',
   'PYMODULE'),
  ('pandas.tests.window.test_online',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\window\\test_online.py',
   'PYMODULE'),
  ('pandas.tests.window.test_pairwise',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\window\\test_pairwise.py',
   'PYMODULE'),
  ('pandas.tests.window.test_rolling',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\window\\test_rolling.py',
   'PYMODULE'),
  ('pandas.tests.window.test_rolling_functions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\window\\test_rolling_functions.py',
   'PYMODULE'),
  ('pandas.tests.window.test_rolling_quantile',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\window\\test_rolling_quantile.py',
   'PYMODULE'),
  ('pandas.tests.window.test_rolling_skew_kurt',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\window\\test_rolling_skew_kurt.py',
   'PYMODULE'),
  ('pandas.tests.window.test_timeseries_window',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\window\\test_timeseries_window.py',
   'PYMODULE'),
  ('pandas.tests.window.test_win_type',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tests\\window\\test_win_type.py',
   'PYMODULE'),
  ('pandas.tseries',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._doctools',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\util\\_doctools.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._test_decorators',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\util\\_test_decorators.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pathlib', 'E:\\python\\Lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'E:\\python\\Lib\\pdb.py', 'PYMODULE'),
  ('pefile',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pefile.py',
   'PYMODULE'),
  ('pickle', 'E:\\python\\Lib\\pickle.py', 'PYMODULE'),
  ('pickletools', 'E:\\python\\Lib\\pickletools.py', 'PYMODULE'),
  ('pkg_resources',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'E:\\python\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'E:\\python\\Lib\\platform.py', 'PYMODULE'),
  ('platformdirs',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('platformdirs.android',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\platformdirs\\android.py',
   'PYMODULE'),
  ('platformdirs.api',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\platformdirs\\api.py',
   'PYMODULE'),
  ('platformdirs.macos',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\platformdirs\\macos.py',
   'PYMODULE'),
  ('platformdirs.unix',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\platformdirs\\unix.py',
   'PYMODULE'),
  ('platformdirs.version',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\platformdirs\\version.py',
   'PYMODULE'),
  ('platformdirs.windows',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\platformdirs\\windows.py',
   'PYMODULE'),
  ('playwright',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\__init__.py',
   'PYMODULE'),
  ('playwright.__main__',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\__main__.py',
   'PYMODULE'),
  ('playwright._impl',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\__init__.py',
   'PYMODULE'),
  ('playwright._impl.__pyinstaller',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\__pyinstaller\\__init__.py',
   'PYMODULE'),
  ('playwright._impl._accessibility',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_accessibility.py',
   'PYMODULE'),
  ('playwright._impl._api_structures',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_api_structures.py',
   'PYMODULE'),
  ('playwright._impl._artifact',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_artifact.py',
   'PYMODULE'),
  ('playwright._impl._assertions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_assertions.py',
   'PYMODULE'),
  ('playwright._impl._async_base',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_async_base.py',
   'PYMODULE'),
  ('playwright._impl._browser',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_browser.py',
   'PYMODULE'),
  ('playwright._impl._browser_context',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_browser_context.py',
   'PYMODULE'),
  ('playwright._impl._browser_type',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_browser_type.py',
   'PYMODULE'),
  ('playwright._impl._cdp_session',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_cdp_session.py',
   'PYMODULE'),
  ('playwright._impl._clock',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_clock.py',
   'PYMODULE'),
  ('playwright._impl._connection',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_connection.py',
   'PYMODULE'),
  ('playwright._impl._console_message',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_console_message.py',
   'PYMODULE'),
  ('playwright._impl._dialog',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_dialog.py',
   'PYMODULE'),
  ('playwright._impl._download',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_download.py',
   'PYMODULE'),
  ('playwright._impl._driver',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_driver.py',
   'PYMODULE'),
  ('playwright._impl._element_handle',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_element_handle.py',
   'PYMODULE'),
  ('playwright._impl._errors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_errors.py',
   'PYMODULE'),
  ('playwright._impl._event_context_manager',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_event_context_manager.py',
   'PYMODULE'),
  ('playwright._impl._fetch',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_fetch.py',
   'PYMODULE'),
  ('playwright._impl._file_chooser',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_file_chooser.py',
   'PYMODULE'),
  ('playwright._impl._frame',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_frame.py',
   'PYMODULE'),
  ('playwright._impl._glob',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_glob.py',
   'PYMODULE'),
  ('playwright._impl._greenlets',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_greenlets.py',
   'PYMODULE'),
  ('playwright._impl._har_router',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_har_router.py',
   'PYMODULE'),
  ('playwright._impl._helper',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_helper.py',
   'PYMODULE'),
  ('playwright._impl._impl_to_api_mapping',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_impl_to_api_mapping.py',
   'PYMODULE'),
  ('playwright._impl._input',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_input.py',
   'PYMODULE'),
  ('playwright._impl._js_handle',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_js_handle.py',
   'PYMODULE'),
  ('playwright._impl._json_pipe',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_json_pipe.py',
   'PYMODULE'),
  ('playwright._impl._local_utils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_local_utils.py',
   'PYMODULE'),
  ('playwright._impl._locator',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_locator.py',
   'PYMODULE'),
  ('playwright._impl._map',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_map.py',
   'PYMODULE'),
  ('playwright._impl._network',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_network.py',
   'PYMODULE'),
  ('playwright._impl._object_factory',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_object_factory.py',
   'PYMODULE'),
  ('playwright._impl._page',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_page.py',
   'PYMODULE'),
  ('playwright._impl._path_utils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_path_utils.py',
   'PYMODULE'),
  ('playwright._impl._playwright',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_playwright.py',
   'PYMODULE'),
  ('playwright._impl._selectors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_selectors.py',
   'PYMODULE'),
  ('playwright._impl._set_input_files_helpers',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_set_input_files_helpers.py',
   'PYMODULE'),
  ('playwright._impl._str_utils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_str_utils.py',
   'PYMODULE'),
  ('playwright._impl._stream',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_stream.py',
   'PYMODULE'),
  ('playwright._impl._sync_base',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py',
   'PYMODULE'),
  ('playwright._impl._tracing',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_tracing.py',
   'PYMODULE'),
  ('playwright._impl._transport',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_transport.py',
   'PYMODULE'),
  ('playwright._impl._video',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_video.py',
   'PYMODULE'),
  ('playwright._impl._waiter',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_waiter.py',
   'PYMODULE'),
  ('playwright._impl._web_error',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_web_error.py',
   'PYMODULE'),
  ('playwright._impl._writable_stream',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_impl\\_writable_stream.py',
   'PYMODULE'),
  ('playwright._repo_version',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\_repo_version.py',
   'PYMODULE'),
  ('playwright.async_api',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\async_api\\__init__.py',
   'PYMODULE'),
  ('playwright.async_api._context_manager',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\async_api\\_context_manager.py',
   'PYMODULE'),
  ('playwright.async_api._generated',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\async_api\\_generated.py',
   'PYMODULE'),
  ('playwright.sync_api',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\sync_api\\__init__.py',
   'PYMODULE'),
  ('playwright.sync_api._context_manager',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\sync_api\\_context_manager.py',
   'PYMODULE'),
  ('playwright.sync_api._generated',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py',
   'PYMODULE'),
  ('plistlib', 'E:\\python\\Lib\\plistlib.py', 'PYMODULE'),
  ('pprint', 'E:\\python\\Lib\\pprint.py', 'PYMODULE'),
  ('propcache',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\propcache\\__init__.py',
   'PYMODULE'),
  ('propcache._helpers',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\propcache\\_helpers.py',
   'PYMODULE'),
  ('propcache._helpers_py',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\propcache\\_helpers_py.py',
   'PYMODULE'),
  ('propcache.api',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\propcache\\api.py',
   'PYMODULE'),
  ('psutil',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile', 'E:\\python\\Lib\\py_compile.py', 'PYMODULE'),
  ('pydoc', 'E:\\python\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data', 'E:\\python\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('pydoc_data.topics', 'E:\\python\\Lib\\pydoc_data\\topics.py', 'PYMODULE'),
  ('pyee',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyee\\__init__.py',
   'PYMODULE'),
  ('pyee.asyncio',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyee\\asyncio.py',
   'PYMODULE'),
  ('pyee.base',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyee\\base.py',
   'PYMODULE'),
  ('pyreadline3',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.api',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\clipboard\\api.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.get_clipboard_text_and_convert',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\clipboard\\get_clipboard_text_and_convert.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.ironpython_clipboard',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\clipboard\\ironpython_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.no_clipboard',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\clipboard\\no_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.win32_clipboard',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\clipboard\\win32_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.console',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\console\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.console.ansi',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\console\\ansi.py',
   'PYMODULE'),
  ('pyreadline3.console.console',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\console\\console.py',
   'PYMODULE'),
  ('pyreadline3.console.event',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\console\\event.py',
   'PYMODULE'),
  ('pyreadline3.console.ironpython_console',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\console\\ironpython_console.py',
   'PYMODULE'),
  ('pyreadline3.error',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\error.py',
   'PYMODULE'),
  ('pyreadline3.keysyms',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\keysyms\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.common',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\keysyms\\common.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.ironpython_keysyms',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\keysyms\\ironpython_keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.keysyms',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\keysyms\\keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.winconstants',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\keysyms\\winconstants.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\lineeditor\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.history',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\lineeditor\\history.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.lineobj',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\lineeditor\\lineobj.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.wordmatcher',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\lineeditor\\wordmatcher.py',
   'PYMODULE'),
  ('pyreadline3.logger',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\logger\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.logger.control',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\logger\\control.py',
   'PYMODULE'),
  ('pyreadline3.logger.log',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\logger\\log.py',
   'PYMODULE'),
  ('pyreadline3.logger.logger',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\logger\\logger.py',
   'PYMODULE'),
  ('pyreadline3.logger.null_handler',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\logger\\null_handler.py',
   'PYMODULE'),
  ('pyreadline3.logger.socket_stream',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\logger\\socket_stream.py',
   'PYMODULE'),
  ('pyreadline3.modes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\modes\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes.basemode',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\modes\\basemode.py',
   'PYMODULE'),
  ('pyreadline3.modes.emacs',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\modes\\emacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.notemacs',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\modes\\notemacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.vi',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\modes\\vi.py',
   'PYMODULE'),
  ('pyreadline3.py3k_compat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\py3k_compat.py',
   'PYMODULE'),
  ('pyreadline3.rlmain',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\rlmain.py',
   'PYMODULE'),
  ('pyreadline3.unicode_helper',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pyreadline3\\unicode_helper.py',
   'PYMODULE'),
  ('pytz',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('queue', 'E:\\python\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'E:\\python\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'E:\\python\\Lib\\random.py', 'PYMODULE'),
  ('readline',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\readline.py',
   'PYMODULE'),
  ('requests',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rlcompleter', 'E:\\python\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('runpy', 'E:\\python\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'E:\\python\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'E:\\python\\Lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'E:\\python\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'E:\\python\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'E:\\python\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'E:\\python\\Lib\\site.py', 'PYMODULE'),
  ('six', 'E:\\env\\playwrightEnv\\Lib\\site-packages\\six.py', 'PYMODULE'),
  ('smtplib', 'E:\\python\\Lib\\smtplib.py', 'PYMODULE'),
  ('socket', 'E:\\python\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'E:\\python\\Lib\\socketserver.py', 'PYMODULE'),
  ('sqlite3', 'E:\\python\\Lib\\sqlite3\\__init__.py', 'PYMODULE'),
  ('sqlite3.__main__', 'E:\\python\\Lib\\sqlite3\\__main__.py', 'PYMODULE'),
  ('sqlite3.dbapi2', 'E:\\python\\Lib\\sqlite3\\dbapi2.py', 'PYMODULE'),
  ('sqlite3.dump', 'E:\\python\\Lib\\sqlite3\\dump.py', 'PYMODULE'),
  ('ssl', 'E:\\python\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'E:\\python\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'E:\\python\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'E:\\python\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'E:\\python\\Lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'E:\\python\\Lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'E:\\python\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'E:\\python\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'E:\\python\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'E:\\python\\Lib\\threading.py', 'PYMODULE'),
  ('timeit', 'E:\\python\\Lib\\timeit.py', 'PYMODULE'),
  ('token', 'E:\\python\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'E:\\python\\Lib\\tokenize.py', 'PYMODULE'),
  ('tomllib', 'E:\\python\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser', 'E:\\python\\Lib\\tomllib\\_parser.py', 'PYMODULE'),
  ('tomllib._re', 'E:\\python\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('tomllib._types', 'E:\\python\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tracemalloc', 'E:\\python\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'E:\\python\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'E:\\python\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('tzdata',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\tzdata\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\tzdata\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Africa',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Argentina',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Indiana',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Kentucky',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.North_Dakota',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Antarctica',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Arctic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\tzdata\\zoneinfo\\Arctic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Asia',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Atlantic',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Australia',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Brazil',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Canada',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Chile',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\tzdata\\zoneinfo\\Chile\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Etc',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Europe',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Indian',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Mexico',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\tzdata\\zoneinfo\\Mexico\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Pacific',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.US',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\__init__.py',
   'PYMODULE'),
  ('unittest', 'E:\\python\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest._log', 'E:\\python\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.async_case',
   'E:\\python\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case', 'E:\\python\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest.loader', 'E:\\python\\Lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.main', 'E:\\python\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.mock', 'E:\\python\\Lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest.result', 'E:\\python\\Lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.runner', 'E:\\python\\Lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.signals', 'E:\\python\\Lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.suite', 'E:\\python\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.util', 'E:\\python\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('urllib', 'E:\\python\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'E:\\python\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'E:\\python\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'E:\\python\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response', 'E:\\python\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib3',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uuid', 'E:\\python\\Lib\\uuid.py', 'PYMODULE'),
  ('webbrowser', 'E:\\python\\Lib\\webbrowser.py', 'PYMODULE'),
  ('win32ctypes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\win32ctypes\\__init__.py',
   'PYMODULE'),
  ('win32ctypes.core',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\win32ctypes\\core\\__init__.py',
   'PYMODULE'),
  ('win32ctypes.core._winerrors',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\win32ctypes\\core\\_winerrors.py',
   'PYMODULE'),
  ('win32ctypes.core.compat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\win32ctypes\\core\\compat.py',
   'PYMODULE'),
  ('win32ctypes.core.ctypes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\win32ctypes\\core\\ctypes\\__init__.py',
   'PYMODULE'),
  ('win32ctypes.core.ctypes._authentication',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\win32ctypes\\core\\ctypes\\_authentication.py',
   'PYMODULE'),
  ('win32ctypes.core.ctypes._common',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\win32ctypes\\core\\ctypes\\_common.py',
   'PYMODULE'),
  ('win32ctypes.core.ctypes._dll',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\win32ctypes\\core\\ctypes\\_dll.py',
   'PYMODULE'),
  ('win32ctypes.core.ctypes._nl_support',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\win32ctypes\\core\\ctypes\\_nl_support.py',
   'PYMODULE'),
  ('win32ctypes.core.ctypes._resource',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\win32ctypes\\core\\ctypes\\_resource.py',
   'PYMODULE'),
  ('win32ctypes.core.ctypes._system_information',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\win32ctypes\\core\\ctypes\\_system_information.py',
   'PYMODULE'),
  ('win32ctypes.core.ctypes._time',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\win32ctypes\\core\\ctypes\\_time.py',
   'PYMODULE'),
  ('win32ctypes.core.ctypes._util',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\win32ctypes\\core\\ctypes\\_util.py',
   'PYMODULE'),
  ('win32ctypes.pywin32',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\win32ctypes\\pywin32\\__init__.py',
   'PYMODULE'),
  ('win32ctypes.pywin32.pywintypes',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\win32ctypes\\pywin32\\pywintypes.py',
   'PYMODULE'),
  ('win32ctypes.pywin32.win32api',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\win32ctypes\\pywin32\\win32api.py',
   'PYMODULE'),
  ('win32ctypes.pywin32.win32cred',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\win32ctypes\\pywin32\\win32cred.py',
   'PYMODULE'),
  ('win32ctypes.version',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\win32ctypes\\version.py',
   'PYMODULE'),
  ('xml', 'E:\\python\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom', 'E:\\python\\Lib\\xml\\dom\\__init__.py', 'PYMODULE'),
  ('xml.dom.NodeFilter',
   'E:\\python\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg', 'E:\\python\\Lib\\xml\\dom\\domreg.py', 'PYMODULE'),
  ('xml.dom.expatbuilder',
   'E:\\python\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'E:\\python\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom', 'E:\\python\\Lib\\xml\\dom\\minidom.py', 'PYMODULE'),
  ('xml.dom.pulldom', 'E:\\python\\Lib\\xml\\dom\\pulldom.py', 'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'E:\\python\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree', 'E:\\python\\Lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('xml.etree.ElementInclude',
   'E:\\python\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'E:\\python\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'E:\\python\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'E:\\python\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers', 'E:\\python\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat', 'E:\\python\\Lib\\xml\\parsers\\expat.py', 'PYMODULE'),
  ('xml.sax', 'E:\\python\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'E:\\python\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'E:\\python\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler', 'E:\\python\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax.saxutils', 'E:\\python\\Lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('xml.sax.xmlreader', 'E:\\python\\Lib\\xml\\sax\\xmlreader.py', 'PYMODULE'),
  ('xmlrpc', 'E:\\python\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'E:\\python\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('yarl',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\yarl\\__init__.py',
   'PYMODULE'),
  ('yarl._parse',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\yarl\\_parse.py',
   'PYMODULE'),
  ('yarl._path',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\yarl\\_path.py',
   'PYMODULE'),
  ('yarl._query',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\yarl\\_query.py',
   'PYMODULE'),
  ('yarl._quoters',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\yarl\\_quoters.py',
   'PYMODULE'),
  ('yarl._quoting',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\yarl\\_quoting.py',
   'PYMODULE'),
  ('yarl._quoting_py',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\yarl\\_quoting_py.py',
   'PYMODULE'),
  ('yarl._url',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\yarl\\_url.py',
   'PYMODULE'),
  ('zipfile', 'E:\\python\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path', 'E:\\python\\Lib\\zipfile\\_path\\__init__.py', 'PYMODULE'),
  ('zipfile._path.glob',
   'E:\\python\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport', 'E:\\python\\Lib\\zipimport.py', 'PYMODULE'),
  ('zipp',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\zipp\\__init__.py',
   'PYMODULE'),
  ('zipp._functools',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\zipp\\_functools.py',
   'PYMODULE'),
  ('zipp.compat',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('zipp.compat.overlay',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\zipp\\compat\\overlay.py',
   'PYMODULE'),
  ('zipp.compat.py310',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('zipp.glob',
   'E:\\env\\playwrightEnv\\Lib\\site-packages\\zipp\\glob.py',
   'PYMODULE'),
  ('zoneinfo', 'E:\\python\\Lib\\zoneinfo\\__init__.py', 'PYMODULE'),
  ('zoneinfo._common', 'E:\\python\\Lib\\zoneinfo\\_common.py', 'PYMODULE'),
  ('zoneinfo._tzpath', 'E:\\python\\Lib\\zoneinfo\\_tzpath.py', 'PYMODULE'),
  ('zoneinfo._zoneinfo',
   'E:\\python\\Lib\\zoneinfo\\_zoneinfo.py',
   'PYMODULE')])
