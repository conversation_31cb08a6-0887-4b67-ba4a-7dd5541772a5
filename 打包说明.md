# 华为/荣耀设备保修查询工具 - 打包说明

## 项目概述

这是一个基于PyQt6的华为/荣耀设备保修查询工具，支持批量查询设备保修信息，具有图形化界面和自动验证码识别功能。

## 项目结构

```
项目根目录/
├── honor_warranty_app.py          # 主应用程序文件 (GUI界面)
├── honor_warranty_querier.py      # 荣耀设备查询逻辑
├── huawei_new2_update_3.py        # 华为设备查询逻辑
├── disclaimer_txt.py              # 免责声明文本
├── honor_logo.ico                 # 应用程序图标
├── honor_logo.png                 # 荣耀Logo图片
├── warning_icon.png               # 警告图标
├── VC_redist.x64.exe              # Visual C++ 运行时库
├── requirements.txt               # Python依赖列表
├── check_dependencies.py          # 依赖检查和安装脚本
├── build_exe.py                   # 打包脚本
├── honor_warranty_app.spec        # PyInstaller配置文件
└── 打包说明.md                    # 本文件
```

## 技术栈

- **GUI框架**: PyQt6
- **异步处理**: asyncio
- **网页自动化**: Playwright (Chromium)
- **验证码识别**: ddddocr
- **数据处理**: pandas, openpyxl
- **网络请求**: aiohttp
- **图像处理**: Pillow, opencv-python-headless

## 打包方法

### 方法一：使用自动打包脚本（推荐）

1. **安装PyInstaller**
   ```bash
   pip install pyinstaller
   ```

2. **运行打包脚本**
   ```bash
   python build_exe.py
   ```

3. **打包完成后，在`dist`目录中会生成以下文件：**
   - `华为荣耀设备保修查询工具.exe` - 主程序
   - `check_dependencies.py` - 依赖检查脚本
   - `启动程序.bat` - 启动脚本（包含依赖检查）
   - `使用说明.txt` - 使用说明文档

### 方法二：使用spec文件

1. **使用spec文件打包**
   ```bash
   pyinstaller honor_warranty_app.spec
   ```

### 方法三：手动命令行打包

```bash
pyinstaller --onefile --windowed --name "华为荣耀设备保修查询工具" ^
    --icon honor_logo.ico ^
    --add-data "honor_logo.ico;." ^
    --add-data "honor_logo.png;." ^
    --add-data "warning_icon.png;." ^
    --add-data "disclaimer_txt.py;." ^
    --add-data "VC_redist.x64.exe;." ^
    --hidden-import PyQt6.QtCore ^
    --hidden-import PyQt6.QtGui ^
    --hidden-import PyQt6.QtWidgets ^
    --hidden-import playwright ^
    --hidden-import ddddocr ^
    --hidden-import pandas ^
    --hidden-import openpyxl ^
    --collect-all PyQt6 ^
    --collect-all playwright ^
    honor_warranty_app.py
```

## 依赖管理策略

### 设计理念

本项目采用"运行时依赖检查"的策略：
- **exe文件**: 只包含Python解释器和核心代码
- **依赖包**: 在首次运行时自动检查和安装
- **浏览器**: Playwright的Chromium在首次运行时下载

### 优势

1. **exe文件体积小**: 避免将所有依赖打包进exe
2. **更新灵活**: 依赖可以独立更新
3. **兼容性好**: 适应不同的系统环境
4. **维护简单**: 依赖问题可以通过重新安装解决

### 首次运行流程

1. 用户双击`启动程序.bat`
2. 脚本检查Python环境
3. 运行`check_dependencies.py`检查依赖
4. 自动安装缺失的依赖包
5. 安装Playwright Chromium浏览器
6. 启动主程序`华为荣耀设备保修查询工具.exe`

## 用户使用说明

### 系统要求

- Windows 10/11 (64位)
- Python 3.8 或更高版本
- 网络连接（用于安装依赖和查询）

### 安装步骤

1. **安装Python**
   - 从 https://www.python.org/downloads/ 下载Python 3.8+
   - 安装时勾选"Add Python to PATH"

2. **首次运行**
   - 双击`启动程序.bat`
   - 等待依赖自动安装（可能需要几分钟）
   - 程序会自动启动

3. **后续使用**
   - 可直接双击`华为荣耀设备保修查询工具.exe`
   - 或继续使用`启动程序.bat`

### 故障排除

1. **Python未安装**
   - 错误信息：未检测到Python环境
   - 解决方法：安装Python 3.8+

2. **依赖安装失败**
   - 检查网络连接
   - 尝试以管理员权限运行
   - 手动安装：`pip install -r requirements.txt`

3. **Playwright浏览器下载失败**
   - 检查网络连接
   - 手动安装：`python -m playwright install chromium`

## 开发者说明

### 修改打包配置

1. **修改依赖列表**: 编辑`check_dependencies.py`中的`REQUIRED_PACKAGES`
2. **修改打包参数**: 编辑`build_exe.py`或`honor_warranty_app.spec`
3. **添加资源文件**: 在`DATA_FILES`中添加新的资源文件

### 测试打包结果

1. **清理环境测试**
   ```bash
   # 创建新的虚拟环境测试
   python -m venv test_env
   test_env\Scripts\activate
   # 只安装PyInstaller，不安装其他依赖
   pip install pyinstaller
   # 运行打包后的exe测试依赖安装
   ```

2. **不同系统测试**
   - 在不同版本的Windows上测试
   - 测试有无Python环境的机器

### 版本发布

1. **更新版本信息**: 修改`build_exe.py`中的版本信息
2. **更新依赖**: 检查并更新`requirements.txt`
3. **测试完整流程**: 从打包到用户首次使用
4. **准备发布包**: 将`dist`目录打包为zip文件

## 注意事项

1. **文件路径**: 确保所有资源文件使用`resource_path()`函数获取路径
2. **编码问题**: 所有文本文件使用UTF-8编码
3. **权限问题**: 建议用户以管理员权限运行以避免安装依赖时的权限问题
4. **网络依赖**: 程序需要网络连接才能正常工作
5. **杀毒软件**: 某些杀毒软件可能误报，需要添加白名单

## 更新日志

### v1.0.0
- 初始版本
- 支持华为和荣耀设备保修查询
- 自动验证码识别
- 批量查询功能
- 代理支持
- 运行时依赖检查和安装
