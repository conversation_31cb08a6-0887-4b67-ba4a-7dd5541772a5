华为/荣耀设备保修查询工具 使用说明

========================================
系统要求
========================================
- Windows 10/11 (64位)
- Python 3.8 或更高版本
- 网络连接

========================================
首次使用
========================================
1. 确保已安装Python 3.8或更高版本
2. 双击运行 "启动程序.bat"
3. 程序会自动检查并安装所需依赖
4. 依赖安装完成后，程序会自动启动

========================================
直接运行
========================================
如果依赖已安装，可以直接双击运行:
"华为荣耀设备保修查询工具.exe"

========================================
依赖说明
========================================
程序需要以下主要依赖:
- PyQt6: GUI界面框架
- Playwright: 网页自动化
- ddddocr: 验证码识别
- pandas: 数据处理
- aiohttp: 异步网络请求

首次运行时会自动安装这些依赖，请确保网络连接正常。

========================================
注意事项
========================================
1. 程序需要网络连接才能正常工作
2. 首次运行可能需要较长时间安装依赖
3. 如遇到问题，请检查网络连接和Python环境
4. 建议使用管理员权限运行以避免权限问题

========================================
故障排除
========================================
如果程序无法启动:
1. 检查Python是否正确安装
2. 检查网络连接是否正常
3. 尝试以管理员权限运行
4. 手动安装依赖: pip install -r requirements.txt

========================================
版本信息
========================================
程序版本: 1.0.0
打包时间: 2025-05-25 18:34:33
Python版本: 3.12.7 (tags/v3.12.7:0b05ead, Oct  1 2024, 03:06:41) [MSC v.1941 64 bit (AMD64)]

========================================
