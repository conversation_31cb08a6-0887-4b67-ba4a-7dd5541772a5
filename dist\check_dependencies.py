#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖检查和安装脚本
在首次运行exe之前，会自动检查并安装所需的依赖
"""

import subprocess
import sys
import os
import importlib
import time
from pathlib import Path

# 所需的依赖包列表
REQUIRED_PACKAGES = [
    "aiohappyeyeballs==2.6.1",
    "aiohttp==3.11.18", 
    "aiosignal==1.3.2",
    "altgraph==0.17.4",
    "anyio==4.9.0",
    "attrs==25.3.0",
    "autocommand==2.2.2",
    "black==25.1.0",
    "certifi==2025.4.26",
    "charset-normalizer==3.4.2",
    "click==8.2.1",
    "colorama==0.4.6",
    "coloredlogs==15.0.1",
    "ddddocr==1.5.6",
    "et_xmlfile==2.0.0",
    "flatbuffers==25.2.10",
    "frozenlist==1.6.0",
    "greenlet==3.2.2",
    "h11==0.16.0",
    "httpcore==1.0.9",
    "httpx==0.28.1",
    "humanfriendly==10.0",
    "idna==3.10",
    "importlib_metadata==8.7.0",
    "more-itertools==10.7.0",
    "mpmath==1.3.0",
    "multidict==6.4.4",
    "mypy_extensions==1.1.0",
    "numpy==2.2.6",
    "onnxruntime==1.19.0",
    "opencv-python-headless==*********",
    "openpyxl==3.1.5",
    "packaging==25.0",
    "pandas==2.2.3",
    "pathspec==0.12.1",
    "pefile==2023.2.7",
    "pillow==11.2.1",
    "pip==25.1.1",
    "platformdirs==4.3.8",
    "playwright==1.52.0",
    "propcache==0.3.1",
    "protobuf==6.31.0",
    "psutil==7.0.0",
    "pyee==13.0.0",
    "pyinstaller==6.13.0",
    "pyinstaller-hooks-contrib==2025.4",
    "PyQt6==6.9.0",
    "PyQt6-Qt6==6.9.0",
    "PyQt6_sip==13.10.0",
    "pyreadline3==3.5.4",
    "python-dateutil==2.9.0.post0",
    "pytz==2025.2",
    "pywin32-ctypes==0.2.3",
    "requests==2.32.3",
    "setuptools==80.8.0",
    "six==1.17.0",
    "sniffio==1.3.1",
    "sympy==1.14.0",
    "typing_extensions==4.13.2",
    "tzdata==2025.2",
    "urllib3==2.4.0",
    "yarl==1.20.0",
    "zipp==3.21.0"
]

def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"错误: 需要Python 3.8或更高版本，当前版本: {version.major}.{version.minor}.{version.micro}")
        return False
    print(f"Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
    return True

def is_package_installed(package_name):
    """检查包是否已安装"""
    try:
        # 提取包名（去掉版本号）
        pkg_name = package_name.split('==')[0].replace('-', '_')
        importlib.import_module(pkg_name)
        return True
    except ImportError:
        return False

def install_package(package):
    """安装单个包"""
    try:
        print(f"正在安装 {package}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package, "--upgrade"
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✓ {package} 安装成功")
            return True
        else:
            print(f"✗ {package} 安装失败: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print(f"✗ {package} 安装超时")
        return False
    except Exception as e:
        print(f"✗ {package} 安装出错: {e}")
        return False

def install_playwright_browsers():
    """安装Playwright浏览器"""
    print("\n正在安装Playwright浏览器（Chromium）...")
    try:
        result = subprocess.run([
            sys.executable, "-m", "playwright", "install", "chromium"
        ], capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("✓ Playwright Chromium 安装成功")
            return True
        else:
            print(f"✗ Playwright Chromium 安装失败: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("✗ Playwright Chromium 安装超时")
        return False
    except Exception as e:
        print(f"✗ Playwright Chromium 安装出错: {e}")
        return False

def check_and_install_dependencies():
    """检查并安装所有依赖"""
    print("=" * 60)
    print("华为/荣耀设备保修查询工具 - 依赖检查")
    print("=" * 60)
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    print(f"\n开始检查 {len(REQUIRED_PACKAGES)} 个依赖包...")
    
    missing_packages = []
    for package in REQUIRED_PACKAGES:
        package_name = package.split('==')[0]
        if not is_package_installed(package_name):
            missing_packages.append(package)
    
    if not missing_packages:
        print("✓ 所有依赖包已安装")
    else:
        print(f"\n发现 {len(missing_packages)} 个缺失的依赖包，开始安装...")
        
        failed_packages = []
        for package in missing_packages:
            if not install_package(package):
                failed_packages.append(package)
        
        if failed_packages:
            print(f"\n以下 {len(failed_packages)} 个包安装失败:")
            for pkg in failed_packages:
                print(f"  - {pkg}")
            return False
    
    # 安装Playwright浏览器
    if not install_playwright_browsers():
        return False
    
    print("\n" + "=" * 60)
    print("✓ 所有依赖安装完成！")
    print("=" * 60)
    return True

def main():
    """主函数"""
    try:
        success = check_and_install_dependencies()
        if success:
            print("\n依赖检查和安装完成，程序即将启动...")
            time.sleep(2)
            return True
        else:
            print("\n依赖安装失败，请检查网络连接或手动安装依赖")
            input("按回车键退出...")
            return False
    except KeyboardInterrupt:
        print("\n\n用户取消安装")
        return False
    except Exception as e:
        print(f"\n依赖检查过程中发生错误: {e}")
        input("按回车键退出...")
        return False

if __name__ == "__main__":
    main()
