#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
版本信息文件
包含应用程序的版本信息和元数据
"""

# 应用程序版本信息
APP_NAME = "华为/荣耀设备保修查询工具"
APP_VERSION = "1.0.0"
APP_DESCRIPTION = "华为和荣耀设备保修信息查询工具，支持批量查询和自动验证码识别"
APP_AUTHOR = "开发团队"
APP_COPYRIGHT = "Copyright © 2025"

# 构建信息
BUILD_DATE = "2025-01-27"
PYTHON_VERSION_REQUIRED = "3.8+"

# 依赖版本信息
DEPENDENCIES = {
    "PyQt6": "6.9.0",
    "playwright": "1.52.0", 
    "ddddocr": "1.5.6",
    "pandas": "2.2.3",
    "aiohttp": "3.11.18",
    "pillow": "11.2.1",
    "numpy": "2.2.6",
    "openpyxl": "3.1.5",
}

# 功能特性
FEATURES = [
    "华为设备保修查询",
    "荣耀设备保修查询", 
    "批量查询支持",
    "Excel文件导入",
    "自动验证码识别",
    "滑块验证码处理",
    "代理支持",
    "结果导出",
    "图形化界面",
    "多线程处理",
]

def get_version_string():
    """获取版本字符串"""
    return f"{APP_NAME} v{APP_VERSION}"

def get_full_version_info():
    """获取完整版本信息"""
    return {
        "name": APP_NAME,
        "version": APP_VERSION,
        "description": APP_DESCRIPTION,
        "author": APP_AUTHOR,
        "copyright": APP_COPYRIGHT,
        "build_date": BUILD_DATE,
        "python_required": PYTHON_VERSION_REQUIRED,
        "dependencies": DEPENDENCIES,
        "features": FEATURES,
    }

def print_version_info():
    """打印版本信息"""
    print("=" * 60)
    print(f"{APP_NAME}")
    print("=" * 60)
    print(f"版本: {APP_VERSION}")
    print(f"描述: {APP_DESCRIPTION}")
    print(f"作者: {APP_AUTHOR}")
    print(f"版权: {APP_COPYRIGHT}")
    print(f"构建日期: {BUILD_DATE}")
    print(f"Python要求: {PYTHON_VERSION_REQUIRED}")
    print()
    print("主要功能:")
    for feature in FEATURES:
        print(f"  - {feature}")
    print()
    print("核心依赖:")
    for dep, version in DEPENDENCIES.items():
        print(f"  - {dep}: {version}")
    print("=" * 60)

if __name__ == "__main__":
    print_version_info()
