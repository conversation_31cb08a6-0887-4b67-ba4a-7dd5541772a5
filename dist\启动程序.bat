@echo off
chcp 65001 >nul
title 华为/荣耀设备保修查询工具

echo ========================================
echo 华为/荣耀设备保修查询工具
echo ========================================
echo.

echo 正在检查运行环境...

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未检测到Python环境
    echo 请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python环境检查通过

REM 检查并安装依赖
echo 正在检查程序依赖...
python check_dependencies.py
if errorlevel 1 (
    echo 依赖安装失败，程序无法启动
    pause
    exit /b 1
)

echo.
echo 启动程序...
"华为荣耀设备保修查询工具.exe"

if errorlevel 1 (
    echo.
    echo 程序运行出错
    pause
)
