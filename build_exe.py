#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyInstaller打包脚本
用于将华为/荣耀设备保修查询工具打包成独立的exe文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

# 项目配置
PROJECT_NAME = "华为荣耀设备保修查询工具"
MAIN_SCRIPT = "honor_warranty_app.py"
ICON_FILE = "honor_logo.ico"
OUTPUT_DIR = "dist"
BUILD_DIR = "build"

# 需要包含的数据文件
DATA_FILES = [
    ("honor_logo.ico", "."),
    ("honor_logo.png", "."),
    ("warning_icon.png", "."),
    ("disclaimer_txt.py", "."),
    ("VC_redist.x64.exe", "."),
]

# 需要包含的隐藏导入
HIDDEN_IMPORTS = [
    "PyQt6.QtCore",
    "PyQt6.QtGui", 
    "PyQt6.QtWidgets",
    "asyncio",
    "aiohttp",
    "playwright",
    "ddddocr",
    "pandas",
    "openpyxl",
    "PIL",
    "cv2",
    "numpy",
    "requests",
    "psutil",
    "multiprocessing",
    "concurrent.futures",
    "sqlite3",
    "json",
    "base64",
    "urllib.parse",
    "urllib.request",
    "xml.etree.ElementTree",
    "email.mime.text",
    "email.mime.multipart",
    "email.mime.base",
    "encodings.utf_8",
    "encodings.gbk",
    "encodings.cp936",
]

def clean_build_dirs():
    """清理构建目录"""
    print("清理构建目录...")
    for dir_name in [BUILD_DIR, OUTPUT_DIR, "__pycache__"]:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"已删除: {dir_name}")

def check_files():
    """检查必要文件是否存在"""
    print("检查必要文件...")
    
    if not os.path.exists(MAIN_SCRIPT):
        print(f"错误: 主脚本文件 {MAIN_SCRIPT} 不存在")
        return False
    
    missing_files = []
    for src, _ in DATA_FILES:
        if not os.path.exists(src):
            missing_files.append(src)
    
    if missing_files:
        print("警告: 以下文件不存在，将跳过:")
        for file in missing_files:
            print(f"  - {file}")
    
    return True

def build_pyinstaller_command():
    """构建PyInstaller命令"""
    cmd = [
        "pyinstaller",
        "--onefile",  # 打包成单个exe文件
        "--windowed",  # 不显示控制台窗口
        "--name", "华为荣耀设备保修查询工具",
        "--distpath", OUTPUT_DIR,
        "--workpath", BUILD_DIR,
        "--clean",  # 清理临时文件
        "--noconfirm",  # 不询问覆盖
    ]
    
    # 添加图标
    if os.path.exists(ICON_FILE):
        cmd.extend(["--icon", ICON_FILE])
    
    # 添加数据文件
    for src, dst in DATA_FILES:
        if os.path.exists(src):
            cmd.extend(["--add-data", f"{src};{dst}"])
    
    # 添加隐藏导入
    for module in HIDDEN_IMPORTS:
        cmd.extend(["--hidden-import", module])
    
    # 添加额外选项
    cmd.extend([
        "--collect-all", "PyQt6",
        "--collect-all", "playwright",
        "--collect-all", "ddddocr",
        "--collect-all", "pandas",
        "--collect-all", "openpyxl",
        "--collect-all", "PIL",
        "--collect-all", "cv2",
        "--collect-all", "numpy",
        "--exclude-module", "tkinter",  # 排除不需要的模块
        "--exclude-module", "matplotlib",
        "--exclude-module", "scipy",
        "--exclude-module", "IPython",
        "--exclude-module", "jupyter",
    ])
    
    # 添加主脚本
    cmd.append(MAIN_SCRIPT)
    
    return cmd

def create_startup_script():
    """创建启动脚本，包含依赖检查"""
    startup_script = """@echo off
chcp 65001 >nul
title 华为/荣耀设备保修查询工具

echo ========================================
echo 华为/荣耀设备保修查询工具
echo ========================================
echo.

echo 正在检查运行环境...

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未检测到Python环境
    echo 请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python环境检查通过

REM 检查并安装依赖
echo 正在检查程序依赖...
python check_dependencies.py
if errorlevel 1 (
    echo 依赖安装失败，程序无法启动
    pause
    exit /b 1
)

echo.
echo 启动程序...
"华为荣耀设备保修查询工具.exe"

if errorlevel 1 (
    echo.
    echo 程序运行出错
    pause
)
"""
    
    with open(os.path.join(OUTPUT_DIR, "启动程序.bat"), "w", encoding="utf-8") as f:
        f.write(startup_script)
    
    print("已创建启动脚本: 启动程序.bat")

def create_readme():
    """创建说明文件"""
    readme_content = """华为/荣耀设备保修查询工具 使用说明

========================================
系统要求
========================================
- Windows 10/11 (64位)
- Python 3.8 或更高版本
- 网络连接

========================================
首次使用
========================================
1. 确保已安装Python 3.8或更高版本
2. 双击运行 "启动程序.bat"
3. 程序会自动检查并安装所需依赖
4. 依赖安装完成后，程序会自动启动

========================================
直接运行
========================================
如果依赖已安装，可以直接双击运行:
"华为荣耀设备保修查询工具.exe"

========================================
依赖说明
========================================
程序需要以下主要依赖:
- PyQt6: GUI界面框架
- Playwright: 网页自动化
- ddddocr: 验证码识别
- pandas: 数据处理
- aiohttp: 异步网络请求

首次运行时会自动安装这些依赖，请确保网络连接正常。

========================================
注意事项
========================================
1. 程序需要网络连接才能正常工作
2. 首次运行可能需要较长时间安装依赖
3. 如遇到问题，请检查网络连接和Python环境
4. 建议使用管理员权限运行以避免权限问题

========================================
故障排除
========================================
如果程序无法启动:
1. 检查Python是否正确安装
2. 检查网络连接是否正常
3. 尝试以管理员权限运行
4. 手动安装依赖: pip install -r requirements.txt

========================================
版本信息
========================================
程序版本: 1.0.0
打包时间: """ + str(__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')) + """
Python版本: """ + sys.version + """

========================================
"""
    
    with open(os.path.join(OUTPUT_DIR, "使用说明.txt"), "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print("已创建使用说明: 使用说明.txt")

def copy_dependency_checker():
    """复制依赖检查脚本到输出目录"""
    if os.path.exists("check_dependencies.py"):
        shutil.copy2("check_dependencies.py", OUTPUT_DIR)
        print("已复制依赖检查脚本到输出目录")

def main():
    """主函数"""
    print("=" * 60)
    print(f"开始打包 {PROJECT_NAME}")
    print("=" * 60)
    
    # 检查文件
    if not check_files():
        return False
    
    # 清理构建目录
    clean_build_dirs()
    
    # 构建PyInstaller命令
    cmd = build_pyinstaller_command()
    
    print("PyInstaller命令:")
    print(" ".join(cmd))
    print()
    
    # 执行打包
    print("开始执行PyInstaller打包...")
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("PyInstaller打包成功!")
    except subprocess.CalledProcessError as e:
        print(f"PyInstaller打包失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False
    
    # 复制依赖检查脚本
    copy_dependency_checker()
    
    # 创建启动脚本
    create_startup_script()
    
    # 创建说明文件
    create_readme()
    
    print("\n" + "=" * 60)
    print("打包完成!")
    print(f"输出目录: {os.path.abspath(OUTPUT_DIR)}")
    print("=" * 60)
    
    # 显示输出文件
    if os.path.exists(OUTPUT_DIR):
        print("\n生成的文件:")
        for file in os.listdir(OUTPUT_DIR):
            file_path = os.path.join(OUTPUT_DIR, file)
            if os.path.isfile(file_path):
                size = os.path.getsize(file_path) / (1024 * 1024)  # MB
                print(f"  - {file} ({size:.1f} MB)")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            input("\n按回车键退出...")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n用户取消打包")
        sys.exit(1)
    except Exception as e:
        print(f"\n打包过程中发生错误: {e}")
        input("按回车键退出...")
        sys.exit(1)
