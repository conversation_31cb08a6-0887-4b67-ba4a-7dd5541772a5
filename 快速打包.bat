@echo off
chcp 65001 >nul
title 华为/荣耀设备保修查询工具 - 快速打包

echo ========================================
echo 华为/荣耀设备保修查询工具 - 快速打包
echo ========================================
echo.

echo 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未检测到Python环境
    echo 请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

echo Python环境检查通过
echo.

echo 检查PyInstaller...
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo PyInstaller未安装，正在安装...
    pip install pyinstaller
    if errorlevel 1 (
        echo PyInstaller安装失败
        pause
        exit /b 1
    )
)

echo PyInstaller检查通过
echo.

echo 开始打包...
python build_exe.py

if errorlevel 1 (
    echo.
    echo 打包失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo 打包完成！
echo ========================================
echo.
echo 生成的文件在 dist 目录中：
echo - 华为荣耀设备保修查询工具.exe
echo - 启动程序.bat
echo - check_dependencies.py
echo - 使用说明.txt
echo.
echo 用户使用方法：
echo 1. 确保已安装Python 3.8+
echo 2. 双击运行"启动程序.bat"
echo 3. 首次运行会自动安装依赖
echo.

pause
