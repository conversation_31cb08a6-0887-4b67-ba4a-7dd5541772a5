# -*- mode: python ; coding: utf-8 -*-
"""
PyInstaller spec文件
用于配置华为/荣耀设备保修查询工具的打包参数
"""

import os
import sys
from pathlib import Path

# 项目根目录
project_root = os.path.dirname(os.path.abspath(SPEC))

# 数据文件列表
datas = [
    (os.path.join(project_root, 'honor_logo.ico'), '.'),
    (os.path.join(project_root, 'honor_logo.png'), '.'),
    (os.path.join(project_root, 'warning_icon.png'), '.'),
    (os.path.join(project_root, 'disclaimer_txt.py'), '.'),
    (os.path.join(project_root, 'VC_redist.x64.exe'), '.'),
]

# 过滤存在的文件
datas = [(src, dst) for src, dst in datas if os.path.exists(src)]

# 隐藏导入
hiddenimports = [
    'PyQt6.QtCore',
    'PyQt6.QtGui',
    'PyQt6.QtWidgets',
    'asyncio',
    'aiohttp',
    'playwright',
    'playwright.async_api',
    'ddddocr',
    'pandas',
    'openpyxl',
    'PIL',
    'PIL.Image',
    'cv2',
    'numpy',
    'requests',
    'psutil',
    'multiprocessing',
    'concurrent.futures',
    'sqlite3',
    'json',
    'base64',
    'urllib.parse',
    'urllib.request',
    'xml.etree.ElementTree',
    'email.mime.text',
    'email.mime.multipart',
    'email.mime.base',
    'encodings.utf_8',
    'encodings.gbk',
    'encodings.cp936',
    'honor_warranty_querier',
    'huawei_new2_update_3',
    'disclaimer_txt',
]

# 排除的模块
excludes = [
    'tkinter',
    'matplotlib',
    'scipy',
    'IPython',
    'jupyter',
    'notebook',
    'qtconsole',
    'spyder',
    'test',
    'tests',
    'testing',
    'unittest',
    'doctest',
    'pdb',
    'pydoc',
]

# 二进制文件
binaries = []

# 分析阶段
a = Analysis(
    [os.path.join(project_root, 'honor_warranty_app.py')],
    pathex=[project_root],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 收集所有文件
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 创建可执行文件
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='华为荣耀设备保修查询工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=os.path.join(project_root, 'honor_logo.ico') if os.path.exists(os.path.join(project_root, 'honor_logo.ico')) else None,
    version_file=None,
)
