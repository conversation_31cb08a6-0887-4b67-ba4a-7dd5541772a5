#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试打包结果的脚本
用于验证打包后的exe文件是否能正常工作
"""

import os
import sys
import subprocess
import tempfile
import shutil
from pathlib import Path

def test_exe_exists():
    """测试exe文件是否存在"""
    exe_path = os.path.join("dist", "华为荣耀设备保修查询工具.exe")
    if os.path.exists(exe_path):
        print("✓ exe文件存在")
        size = os.path.getsize(exe_path) / (1024 * 1024)
        print(f"  文件大小: {size:.1f} MB")
        return True
    else:
        print("✗ exe文件不存在")
        return False

def test_required_files():
    """测试必需文件是否存在"""
    required_files = [
        "dist/华为荣耀设备保修查询工具.exe",
        "dist/check_dependencies.py",
        "dist/启动程序.bat",
        "dist/使用说明.txt"
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {os.path.basename(file_path)} 存在")
        else:
            print(f"✗ {os.path.basename(file_path)} 缺失")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def test_dependency_checker():
    """测试依赖检查脚本"""
    checker_path = os.path.join("dist", "check_dependencies.py")
    if not os.path.exists(checker_path):
        print("✗ 依赖检查脚本不存在")
        return False
    
    try:
        # 测试脚本语法
        result = subprocess.run([
            sys.executable, "-m", "py_compile", checker_path
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 依赖检查脚本语法正确")
            return True
        else:
            print("✗ 依赖检查脚本语法错误")
            print(f"  错误: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ 测试依赖检查脚本时出错: {e}")
        return False

def test_startup_script():
    """测试启动脚本"""
    script_path = os.path.join("dist", "启动程序.bat")
    if not os.path.exists(script_path):
        print("✗ 启动脚本不存在")
        return False
    
    try:
        with open(script_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查关键内容
        required_content = [
            "python --version",
            "check_dependencies.py",
            "华为荣耀设备保修查询工具.exe"
        ]
        
        missing_content = []
        for item in required_content:
            if item not in content:
                missing_content.append(item)
        
        if not missing_content:
            print("✓ 启动脚本内容正确")
            return True
        else:
            print("✗ 启动脚本内容不完整")
            print(f"  缺失: {missing_content}")
            return False
    except Exception as e:
        print(f"✗ 测试启动脚本时出错: {e}")
        return False

def test_exe_basic_run():
    """测试exe文件基本运行（快速退出测试）"""
    exe_path = os.path.join("dist", "华为荣耀设备保修查询工具.exe")
    if not os.path.exists(exe_path):
        print("✗ exe文件不存在，无法测试运行")
        return False
    
    try:
        print("正在测试exe文件基本运行...")
        print("注意: 这将启动程序，请在免责声明对话框中选择'不同意'来快速退出")
        
        # 启动程序，设置较短超时
        process = subprocess.Popen([exe_path], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE)
        
        # 等待5秒，如果程序还在运行说明启动成功
        try:
            stdout, stderr = process.communicate(timeout=5)
            if process.returncode == 0:
                print("✓ exe文件可以正常启动和退出")
                return True
            else:
                print(f"✗ exe文件运行异常，退出码: {process.returncode}")
                if stderr:
                    print(f"  错误输出: {stderr.decode('utf-8', errors='ignore')}")
                return False
        except subprocess.TimeoutExpired:
            # 超时说明程序正在运行，这是好的
            process.terminate()
            try:
                process.wait(timeout=2)
            except subprocess.TimeoutExpired:
                process.kill()
            print("✓ exe文件可以正常启动（程序正在运行）")
            return True
            
    except Exception as e:
        print(f"✗ 测试exe文件运行时出错: {e}")
        return False

def create_test_report():
    """创建测试报告"""
    report_content = f"""华为/荣耀设备保修查询工具 - 打包测试报告

测试时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Python版本: {sys.version}

========================================
测试结果
========================================

"""
    
    tests = [
        ("exe文件存在性测试", test_exe_exists),
        ("必需文件完整性测试", test_required_files),
        ("依赖检查脚本测试", test_dependency_checker),
        ("启动脚本测试", test_startup_script),
    ]
    
    all_passed = True
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = test_func()
            status = "通过" if result else "失败"
            if not result:
                all_passed = False
        except Exception as e:
            print(f"✗ 测试执行出错: {e}")
            result = False
            status = "错误"
            all_passed = False
        
        report_content += f"{test_name}: {status}\n"
    
    # 可选的运行测试
    print(f"\n--- exe文件运行测试 (可选) ---")
    print("是否要测试exe文件运行？这将启动程序界面。")
    choice = input("输入 y 进行测试，其他键跳过: ").lower().strip()
    
    if choice == 'y':
        try:
            result = test_exe_basic_run()
            status = "通过" if result else "失败"
            if not result:
                all_passed = False
        except Exception as e:
            print(f"✗ 运行测试出错: {e}")
            status = "错误"
            all_passed = False
        report_content += f"exe文件运行测试: {status}\n"
    else:
        report_content += f"exe文件运行测试: 跳过\n"
    
    report_content += f"\n总体结果: {'全部通过' if all_passed else '存在问题'}\n"
    
    # 保存报告
    report_path = os.path.join("dist", "测试报告.txt")
    try:
        with open(report_path, "w", encoding="utf-8") as f:
            f.write(report_content)
        print(f"\n测试报告已保存: {report_path}")
    except Exception as e:
        print(f"保存测试报告失败: {e}")
    
    return all_passed

def main():
    """主函数"""
    print("=" * 60)
    print("华为/荣耀设备保修查询工具 - 打包测试")
    print("=" * 60)
    
    if not os.path.exists("dist"):
        print("错误: dist目录不存在，请先运行打包脚本")
        return False
    
    try:
        success = create_test_report()
        
        print("\n" + "=" * 60)
        if success:
            print("✓ 所有测试通过！打包结果正常")
        else:
            print("✗ 部分测试失败，请检查打包结果")
        print("=" * 60)
        
        return success
        
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        input("\n按回车键退出...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n用户取消测试")
        sys.exit(1)
