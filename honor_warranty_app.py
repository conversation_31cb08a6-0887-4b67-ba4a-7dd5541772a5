import sys
import os
import asyncio
import time
import traceback
import pandas as pd
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QTableWidget, QTableWidgetItem, QPushButton,
                            QLabel, QLineEdit, QFileDialog, QMessageBox, QCheckBox,
                            QProgressBar, QComboBox, QGroupBox,
                            QRadioButton, QButtonGroup, QStatusBar, QSplitter,
                            QDialog, QTextEdit, QDialogButtonBox, QTabWidget, QFrame)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect
from PyQt6.QtGui import QFont, QIcon, QColor, QPixmap, QPainter, QPen, QBrush

# 导入免责声明文本
from disclaimer_txt import doc as DISCLAIMER_TEXT

# 导入华为查询功能
from huawei_new2_update_3 import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>a<PERSON><PERSON><PERSON>,
    ProxyManager as HuaweiProxyManager,
    process_single_huawei_sn
)

# Import the HonorWarrantyQuerier and ProxyManager classes from the original script
from honor_warranty_querier import HonorWarrantyQuerier, ProxyManager

class HuaweiWarrantyQueryWorker(QThread):
    """Worker thread to handle Huawei warranty queries without blocking the UI"""
    progress_updated = pyqtSignal(int, str)  # (row_index, status)
    query_completed = pyqtSignal(int, dict)  # (row_index, result_data)
    all_completed = pyqtSignal()

    def __init__(self, serial_numbers, use_proxy=False, proxy_key=""):
        super().__init__()
        self.serial_numbers = serial_numbers
        self.use_proxy = use_proxy
        self.proxy_key = proxy_key
        self.is_running = True
        self.max_concurrent_queries = 2  # Default concurrent queries

    def stop(self):
        self.is_running = False

    async def run_queries(self):
        """Run all Huawei warranty queries asynchronously"""
        from playwright.async_api import async_playwright

        results = []
        proxy_manager = None
        captcha_handler = None

        try:
            if self.use_proxy and self.proxy_key:
                proxy_manager = HuaweiProxyManager(
                    api_url="https://share.proxy.qg.net/get",
                    max_usage_per_ip=4,
                    proxy_key=self.proxy_key
                )

            captcha_handler = SliderCaptchaHandler()

            async with async_playwright() as p:
                browser = await p.chromium.launch(
                    headless=True,
                    args=[
                        '--no-sandbox', '--disable-blink-features=AutomationControlled',
                        '--disable-dev-shm-usage', '--window-size=1280,800',
                    ]
                )

                try:
                    semaphore = asyncio.Semaphore(self.max_concurrent_queries)

                    tasks = []
                    for i, sn in enumerate(self.serial_numbers):
                        if not self.is_running:
                            break

                        # 更新进度
                        self.progress_updated.emit(i, "正在查询...")

                        # 创建任务
                        task = asyncio.create_task(
                            self.process_single_sn(browser, proxy_manager, captcha_handler, sn, i, semaphore)
                        )
                        tasks.append(task)

                    if tasks:
                        results = await asyncio.gather(*tasks, return_exceptions=True)

                        # Process any exceptions in results
                        for i, result in enumerate(results):
                            if isinstance(result, Exception):
                                print(f"Error processing SN {self.serial_numbers[i]}: {result}")
                                error_result = {
                                    "serial_number": self.serial_numbers[i],
                                    "status": "处理异常",
                                    "error_message": str(result),
                                    "data": None,
                                    "duration_seconds": 0.0,
                                    "proxy_used": "未知"
                                }
                                self.query_completed.emit(i, error_result)
                                results[i] = error_result
                finally:
                    # Clean up resources
                    if browser:
                        await browser.close()
        except Exception as e:
            print(f"运行华为查询时发生错误: {e}")
            traceback.print_exc()
        finally:
            if proxy_manager:
                await proxy_manager.close_aio_session_proxy()
            if captcha_handler:
                await captcha_handler.close_aio_session_img()

        return results

    async def process_single_sn(self, browser, proxy_manager, captcha_handler, serial_number, row_index, semaphore):
        """Process a single Huawei serial number query"""
        try:
            # 使用原始的process_single_huawei_sn函数处理查询
            result = await process_single_huawei_sn(
                browser,
                proxy_manager if self.use_proxy else None,
                captcha_handler,
                serial_number,
                semaphore
            )

            # 发送结果信号
            self.query_completed.emit(row_index, result)
            return result
        except Exception as e:
            print(f"处理华为SN {serial_number} 时发生错误: {e}")
            error_result = {
                "serial_number": serial_number,
                "status": "处理异常",
                "error_message": str(e),
                "data": None,
                "duration_seconds": 0.0,
                "proxy_used": "未知"
            }
            self.query_completed.emit(row_index, error_result)
            return error_result

    def run(self):
        """Run the worker thread"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            # 只运行查询，不保存JSON文件
            loop.run_until_complete(self.run_queries())
        except Exception as e:
            print(f"Error in Huawei query worker: {e}")
        finally:
            loop.close()
            self.all_completed.emit()


class AnimatedExitDialog(QDialog):
    """美化的退出确认对话框，带有动态效果"""
    def __init__(self, parent=None, has_running_tasks=False):
        super().__init__(parent)
        self.has_running_tasks = has_running_tasks
        self.setWindowTitle("确认退出")
        # 根据是否有运行任务调整弹窗尺寸
        if has_running_tasks:
            self.setFixedSize(400, 300)  # 有运行任务时增加高度
        else:
            self.setFixedSize(360, 220)  # 无运行任务时使用较小尺寸
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)

        # 设置图标
        icon_path = resource_path("honor_logo.ico")
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        self.setup_ui()
        self.setup_animations()

    def setup_ui(self):
        """设置UI界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)

        # 创建主容器
        self.main_container = QFrame()
        self.main_container.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 12px;
                border: 1px solid #e0e0e0;
            }
        """)
        main_layout.addWidget(self.main_container)

        # 容器布局
        container_layout = QVBoxLayout(self.main_container)
        # 根据是否有运行任务调整内边距和间距
        if self.has_running_tasks:
            container_layout.setContentsMargins(20, 20, 20, 20)  # 有运行任务时增加内边距
            container_layout.setSpacing(18)  # 增加控件间距
        else:
            container_layout.setContentsMargins(20, 18, 20, 18)  # 无运行任务时使用较小内边距
            container_layout.setSpacing(15)  # 较小控件间距

        # 标题区域 - 重新设计为居中布局
        title_layout = QVBoxLayout()
        title_layout.setSpacing(8)

        # 图标居中显示
        icon_layout = QHBoxLayout()
        icon_layout.addStretch()

        self.icon_label = QLabel()
        warning_icon = self.create_warning_icon()
        self.icon_label.setPixmap(warning_icon)
        self.icon_label.setFixedSize(32, 32)  # 进一步减小图标尺寸
        icon_layout.addWidget(self.icon_label)
        icon_layout.addStretch()

        title_layout.addLayout(icon_layout)

        # 标题文本居中显示
        title_label = QLabel("确认退出")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                margin: 0px;
            }
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_layout.addWidget(title_label)

        container_layout.addLayout(title_layout)

        # 消息内容
        if self.has_running_tasks:
            message_text = "检测到有查询任务正在进行中\n退出程序将终止所有正在运行的查询任务\n\n确定要退出程序吗？"
            self.message_label = QLabel(message_text)
            self.message_label.setStyleSheet("""
                QLabel {
                    font-size: 13px;
                    color: #e74c3c;
                    line-height: 1.6;
                    padding: 15px;
                    background-color: #fdf2f2;
                    border: 1px solid #fecaca;
                    border-radius: 6px;
                    min-height: 80px;
                }
            """)
        else:
            message_text = "确定要退出程序吗？"
            self.message_label = QLabel(message_text)
            self.message_label.setStyleSheet("""
                QLabel {
                    font-size: 13px;
                    color: #34495e;
                    line-height: 1.5;
                    padding: 12px;
                    background-color: #f8f9fa;
                    border: 1px solid #e9ecef;
                    border-radius: 6px;
                    min-height: 40px;
                }
            """)

        self.message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.message_label.setWordWrap(True)
        container_layout.addWidget(self.message_label)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)  # 适当的按钮间距
        # 根据是否有运行任务调整按钮区域的边距
        if self.has_running_tasks:
            button_layout.setContentsMargins(0, 15, 0, 0)  # 有运行任务时增加顶部空间
        else:
            button_layout.setContentsMargins(0, 10, 0, 0)  # 无运行任务时使用较小空间

        # 取消按钮
        self.cancel_button = QPushButton("取消")
        self.cancel_button.setFixedSize(90, 34)  # 稍微增大按钮尺寸以保持平衡
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #f8f9fa;
                color: #6c757d;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                font-size: 12px;
                font-weight: 500;
                padding: 2px;
            }
            QPushButton:hover {
                background-color: #e9ecef;
                border: 1px solid #adb5bd;
                color: #495057;
            }
            QPushButton:pressed {
                background-color: #dee2e6;
                border: 1px solid #6c757d;
            }
        """)

        # 确认按钮
        self.confirm_button = QPushButton("确认退出")
        self.confirm_button.setFixedSize(90, 34)  # 与取消按钮保持一致的尺寸
        if self.has_running_tasks:
            self.confirm_button.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: 1px solid #dc3545;
                    border-radius: 6px;
                    font-size: 12px;
                    font-weight: 500;
                    padding: 2px;
                }
                QPushButton:hover {
                    background-color: #c82333;
                    border: 1px solid #bd2130;
                }
                QPushButton:pressed {
                    background-color: #bd2130;
                    border: 1px solid #b21f2d;
                }
            """)
        else:
            self.confirm_button.setStyleSheet("""
                QPushButton {
                    background-color: #2b579a;
                    color: white;
                    border: 1px solid #2b579a;
                    border-radius: 6px;
                    font-size: 12px;
                    font-weight: 500;
                    padding: 2px;
                }
                QPushButton:hover {
                    background-color: #1e3f73;
                    border: 1px solid #1e3f73;
                }
                QPushButton:pressed {
                    background-color: #163056;
                    border: 1px solid #163056;
                }
            """)

        # 使用更好的按钮布局方式
        button_layout.addStretch()  # 左侧弹性空间
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.confirm_button)
        button_layout.addStretch()  # 右侧弹性空间

        container_layout.addLayout(button_layout)

        # 连接信号
        self.cancel_button.clicked.connect(self.reject)
        self.confirm_button.clicked.connect(self.accept)

    def create_warning_icon(self):
        """创建警告图标"""
        pixmap = QPixmap(32, 32)  # 调整为32x32尺寸
        pixmap.fill(Qt.GlobalColor.transparent)

        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # 绘制圆形背景
        if self.has_running_tasks:
            painter.setBrush(QBrush(QColor("#fef2f2")))
            painter.setPen(QPen(QColor("#fecaca"), 1))
        else:
            painter.setBrush(QBrush(QColor("#fff3cd")))
            painter.setPen(QPen(QColor("#ffeaa7"), 1))

        painter.drawEllipse(2, 2, 28, 28)  # 调整圆形尺寸

        # 绘制感叹号
        if self.has_running_tasks:
            painter.setPen(QPen(QColor("#e74c3c"), 2.5, Qt.PenStyle.SolidLine, Qt.PenCapStyle.RoundCap))
        else:
            painter.setPen(QPen(QColor("#f39c12"), 2.5, Qt.PenStyle.SolidLine, Qt.PenCapStyle.RoundCap))

        painter.drawLine(16, 9, 16, 18)  # 调整感叹号位置和尺寸
        painter.drawPoint(16, 23)

        painter.end()
        return pixmap

    def setup_animations(self):
        """设置动画效果"""
        # 缩放动画
        self.scale_animation = QPropertyAnimation(self.main_container, b"geometry")
        self.scale_animation.setDuration(200)
        self.scale_animation.setEasingCurve(QEasingCurve.Type.OutBack)

        # 透明度动画
        self.opacity_animation = QPropertyAnimation(self, b"windowOpacity")
        self.opacity_animation.setDuration(150)
        self.opacity_animation.setEasingCurve(QEasingCurve.Type.OutQuad)

    def showEvent(self, a0):
        """显示事件 - 播放进入动画"""
        super().showEvent(a0)

        # 设置初始状态
        self.setWindowOpacity(0)

        # 计算动画的起始和结束位置
        final_geometry = self.main_container.geometry()
        start_geometry = QRect(
            final_geometry.x() + final_geometry.width() // 4,
            final_geometry.y() + final_geometry.height() // 4,
            final_geometry.width() // 2,
            final_geometry.height() // 2
        )

        self.main_container.setGeometry(start_geometry)

        # 启动动画
        self.scale_animation.setStartValue(start_geometry)
        self.scale_animation.setEndValue(final_geometry)

        self.opacity_animation.setStartValue(0)
        self.opacity_animation.setEndValue(1)

        self.scale_animation.start()
        self.opacity_animation.start()


class DisclaimerDialog(QDialog):
    """免责声明对话框"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("软件免责声明")
        self.setMinimumSize(600, 500)
        icon_path = resource_path("honor_logo.ico")
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        # 创建布局
        layout = QVBoxLayout(self)

        # 添加标题
        title_label = QLabel("软件工具免责声明")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2b579a;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # 添加免责声明文本
        text_edit = QTextEdit()
        text_edit.setReadOnly(True)
        text_edit.setPlainText(DISCLAIMER_TEXT)
        text_edit.setStyleSheet("""
            QTextEdit {
                background-color: #f9f9f9;
                border: 1px solid #cccccc;
                border-radius: 4px;
                padding: 10px;
                font-size: 12px;
            }
        """)
        layout.addWidget(text_edit)

        # 添加按钮
        button_box = QDialogButtonBox()
        self.accept_button = button_box.addButton("同意", QDialogButtonBox.ButtonRole.AcceptRole)
        self.reject_button = button_box.addButton("不同意", QDialogButtonBox.ButtonRole.RejectRole)

        # 设置按钮样式
        if self.accept_button:
            self.accept_button.setStyleSheet("""
                QPushButton {
                    background-color: #2b579a;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 16px;
                    font-weight: bold;
                    min-width: 100px;
                }
                QPushButton:hover {
                    background-color: #3a6bae;
                }
            """)

        if self.reject_button:
            self.reject_button.setStyleSheet("""
                QPushButton {
                    background-color: #f0f0f0;
                    color: #333333;
                    border: 1px solid #cccccc;
                    border-radius: 4px;
                    padding: 8px 16px;
                    min-width: 100px;
                }
                QPushButton:hover {
                    background-color: #e0e0e0;
                }
            """)

        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)

        layout.addWidget(button_box)

class WarrantyQueryWorker(QThread):
    """Worker thread to handle warranty queries without blocking the UI"""
    progress_updated = pyqtSignal(int, str)  # (row_index, status)
    query_completed = pyqtSignal(int, dict)  # (row_index, result_data)
    all_completed = pyqtSignal()

    def __init__(self, serial_numbers, use_proxy=False, proxy_key=""):
        super().__init__()
        self.serial_numbers = serial_numbers
        self.use_proxy = use_proxy
        self.proxy_key = proxy_key
        self.is_running = True
        self.max_concurrent_queries = 2  # Default concurrent queries

    def stop(self):
        self.is_running = False

    async def process_single_sn(self, browser, proxy_manager, querier, serial_number, row_index, semaphore):
        """Process a single serial number query"""
        print(f"\n[查询] 开始处理荣耀设备查询 - 序号: {row_index + 1}, SN: {serial_number}")

        self.progress_updated.emit(row_index, "正在查询...")
        print(f"[信号] 发送进度更新信号 - SN: {serial_number}")

        page = None
        context = None
        start_time = time.time()
        proxy_server_used = None
        query_result = {
            "serial_number": serial_number,
            "status": "初始化失败",
            "data": None,
            "duration_seconds": 0.0,
            "proxy_used": None
        }
        print(f"[查询] 查询结果对象初始化完成")

        try:
            async with semaphore:
                context_options = {
                    "user_agent": 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    "viewport": {'width': 1280, 'height': 800},
                    "java_script_enabled": True,
                }

                print(f"[浏览器] 配置浏览器上下文 - 代理: {'启用' if proxy_manager else '禁用'}")
                if proxy_manager:
                    proxy_config = await proxy_manager.get_proxy_config()
                    if proxy_config and proxy_config.get("server"):
                        proxy_server_used = proxy_config["server"]
                        context_options["proxy"] = proxy_config
                        print(f"[代理] SN: {serial_number} - 将使用代理: {proxy_server_used}")
                    else:
                        print(f"[警告] SN: {serial_number} - (代理已启用但)无法获取有效代理IP，将不使用代理进行尝试。")
                else:
                    print(f"[代理] SN: {serial_number} - 代理未启用，不使用代理。")

                print(f"[浏览器] 创建浏览器上下文和页面...")
                context = await browser.new_context(**context_options)
                page = await context.new_page()
                print(f"[浏览器] 浏览器页面创建成功")

                url = 'https://www.honor.com/cn/support/warranty-query/'
                navigation_successful = False
                print(f"[导航] 导航到保修查询页面: {url}")
                try:
                    # 如果未使用代理，超时可以适当调短一些
                    nav_timeout = 35000 if proxy_server_used else 25000
                    print(f"[导航] 导航超时设置: {nav_timeout/1000:.1f}秒")
                    await page.goto(url, wait_until='load', timeout=nav_timeout)
                    navigation_successful = True
                    print(f"[导航] 页面导航成功")
                except Exception as nav_exc:
                    proxy_info = f"(代理: {proxy_server_used})" if proxy_server_used else "(无代理)"
                    print(f"[错误] SN: {serial_number} - 导航到 {url} 失败 {proxy_info}: {nav_exc}")
                    query_result["status"] = "导航失败"
                    query_result["error_message"] = str(nav_exc)

                if navigation_successful:
                    if proxy_manager and proxy_server_used:
                        await proxy_manager.increment_proxy_usage(proxy_server_used)
                        print(f"[代理] 代理使用计数已更新")

                    print(f"[Cookie] 检查并处理Cookie同意按钮...")
                    try:
                        cookie_button = await page.query_selector("#onetrust-accept-btn-handler")
                        if cookie_button and await cookie_button.is_visible(timeout=2000):
                            await cookie_button.click(timeout=2000)
                            await asyncio.sleep(0.3)
                            print(f"[Cookie] Cookie同意按钮已点击")
                        else:
                            print(f"[Cookie] 未发现Cookie同意按钮")
                    except Exception as e:
                        print(f"[警告] 处理Cookie按钮时出错: {e}")

                    print(f"[查询] 开始保修查询流程...")
                    status = await querier.query_warranty(page, serial_number, max_attempts=3)
                    query_result["status"] = status
                    print(f"[查询] 查询状态: {status}")

                    if status == "成功":
                        print(f"[成功] 查询成功，开始提取结果数据...")
                        await asyncio.sleep(0.3)
                        result_data = await querier.extract_results(page, serial_number)
                        query_result["data"] = result_data
                        print(f"[成功] 结果数据提取完成")
                    else:
                        print(f"[失败] 查询失败，状态: {status}")
                else:
                    print(f"[错误] 页面导航失败，跳过查询流程")

        except Exception as e:
            proxy_info = f"(代理: {proxy_server_used})" if proxy_server_used else "(无代理)"
            print(f"[错误] 处理SN {serial_number} {proxy_info} 时发生严重错误: {e}")
            query_result["status"] = "严重错误"
            query_result["error_message"] = str(e)
        finally:
            print(f"[清理] 清理资源...")
            if page:
                await page.close()
                print(f"[清理] 页面已关闭")
            if context:
                await context.close()
                print(f"[清理] 浏览器上下文已关闭")

            query_result["duration_seconds"] = round(time.time() - start_time, 2)
            query_result["proxy_used"] = proxy_server_used if self.use_proxy else "未启用"

            print(f"[完成] 查询完成 - SN: {serial_number}, 耗时: {query_result['duration_seconds']:.1f}秒, 状态: {query_result['status']}")
            self.query_completed.emit(row_index, query_result)
            return query_result

    async def run_queries(self):
        """Run all warranty queries asynchronously"""
        print("\n" + "=" * 50)
        print("[工作线程] 荣耀设备查询工作线程启动")
        print("=" * 50)

        from playwright.async_api import async_playwright

        results = []
        proxy_manager = None

        try:
            print(f"[配置] 查询配置检查:")
            print(f"   序列号数量: {len(self.serial_numbers)}")
            print(f"   使用代理: {'是' if self.use_proxy else '否'}")
            print(f"   最大并发: {self.max_concurrent_queries}")

            if self.use_proxy and self.proxy_key:
                print(f"[代理] 初始化代理管理器...")
                proxy_manager = ProxyManager(
                    api_url="https://share.proxy.qg.net/get",
                    max_usage_per_ip=4,
                    proxy_key=self.proxy_key
                )
                print(f"[代理] 代理管理器初始化完成")
            else:
                print(f"[代理] 未启用代理或代理密钥为空")

            print(f"[浏览器] 启动Playwright浏览器...")
            async with async_playwright() as p:
                browser = await p.chromium.launch(
                    headless=True,
                    args=[
                        '--no-sandbox', '--disable-blink-features=AutomationControlled',
                        '--disable-dev-shm-usage', '--window-size=1280,800',
                    ]
                )
                print(f"[浏览器] Chromium浏览器启动成功")

                querier = None
                try:
                    print(f"[查询器] 初始化荣耀保修查询器...")
                    querier = HonorWarrantyQuerier()
                    semaphore = asyncio.Semaphore(self.max_concurrent_queries)
                    print(f"[查询器] 查询器和并发控制初始化完成")

                    print(f"[任务] 创建查询任务...")
                    tasks = []
                    for i, sn in enumerate(self.serial_numbers):
                        if not self.is_running:
                            print(f"[警告] 检测到停止信号，停止创建新任务")
                            break
                        tasks.append(self.process_single_sn(browser, proxy_manager, querier, sn, i, semaphore))

                    print(f"[任务] 已创建 {len(tasks)} 个查询任务")

                    if tasks:
                        print(f"[执行] 开始并发执行查询任务...")
                        results = await asyncio.gather(*tasks, return_exceptions=True)
                        print(f"[执行] 所有查询任务执行完成")

                        # Process any exceptions in results
                        print(f"[检查] 检查查询结果中的异常...")
                        exception_count = 0
                        for i, result in enumerate(results):
                            if isinstance(result, Exception):
                                exception_count += 1
                                print(f"[错误] 处理SN {self.serial_numbers[i]} 时发生异常: {result}")
                                results[i] = {
                                    "serial_number": self.serial_numbers[i],
                                    "status": "处理异常",
                                    "error_message": str(result),
                                    "data": None,
                                    "duration_seconds": 0.0,
                                    "proxy_used": "未知"
                                }

                        if exception_count > 0:
                            print(f"[警告] 共发现 {exception_count} 个异常结果")
                        else:
                            print(f"[检查] 所有查询结果正常")
                    else:
                        print(f"[警告] 没有创建任何查询任务")
                finally:
                    # Clean up resources
                    print(f"[清理] 清理查询器资源...")
                    try:
                        if querier:
                            await querier.close_aio_session()
                            print(f"[清理] 查询器会话已关闭")
                    except Exception as e:
                        print(f"[错误] 关闭querier时出错: {e}")

                    print(f"[清理] 关闭浏览器...")
                    await browser.close()
                    print(f"[清理] 浏览器已关闭")
        except Exception as e:
            print(f"[错误] 运行查询时发生错误: {e}")
            traceback.print_exc()
        finally:
            if proxy_manager:
                print(f"[清理] 清理代理管理器...")
                await proxy_manager.close_aio_session()
                print(f"[清理] 代理管理器已清理")

        print("=" * 50)
        print("[工作线程] 荣耀设备查询工作线程完成")
        print("=" * 50)
        return results

    def run(self):
        """Run the worker thread"""
        print("\n" + "=" * 40)
        print("[线程] 荣耀查询工作线程启动")
        print("=" * 40)

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        print("[线程] 异步事件循环创建完成")

        try:
            print("[线程] 开始执行查询任务...")
            # 只运行查询，不保存JSON文件
            loop.run_until_complete(self.run_queries())
            print("[线程] 查询任务执行完成")
        except Exception as e:
            print(f"[错误] 查询工作线程发生错误: {e}")
            traceback.print_exc()
        finally:
            print("[线程] 关闭事件循环...")
            loop.close()
            print("[线程] 事件循环已关闭")

            print("[线程] 发送完成信号...")
            self.all_completed.emit()
            print("[线程] 完成信号已发送")

        print("=" * 40)
        print("[线程] 荣耀查询工作线程结束")
        print("=" * 40)

class HonorWarrantyApp(QMainWindow):
    """Main application window for Honor/Huawei Warranty Query Tool"""

    def __init__(self):
        super().__init__()
        print("=" * 60)
        print("[启动] 华为/荣耀设备保修查询工具启动中...")
        print("=" * 60)

        self.setWindowTitle("华为/荣耀设备保修查询工具")
        self.setMinimumSize(1400, 800)  # 增加最小窗口大小，确保所有UI元素正常显示
        self.resize(1400, 800)  # 设置默认窗口大小
        print("[初始化] 窗口初始化完成 - 尺寸: 1400x800")

        # 设置应用程序图标
        print("[初始化] 正在设置应用程序图标...")
        icon_path = resource_path("honor_logo.ico")
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
            print(f"[初始化] 图标加载成功: {icon_path}")
        else:
            print(f"[警告] 图标文件不存在: {icon_path}")
            print("[信息] 将使用默认系统图标")

        # 设置应用程序样式
        print("[初始化] 正在设置应用程序样式...")
        self.setup_style()
        print("[初始化] 应用程序样式设置完成")

        # 显示免责声明对话框
        print("[初始化] 显示免责声明对话框...")
        self.show_disclaimer()
        print("[初始化] 免责声明确认完成")

        # Initialize UI components
        print("[初始化] 正在初始化UI组件...")
        self.init_ui()
        print("[初始化] UI组件初始化完成")

        # Data storage for Honor
        self.honor_serial_numbers = []
        self.honor_results = []
        self.honor_query_worker = None
        print("[初始化] 荣耀查询数据存储初始化完成")

        # Data storage for Huawei
        self.huawei_serial_numbers = []
        self.huawei_results = []
        self.huawei_query_worker = None
        print("[初始化] 华为查询数据存储初始化完成")

        print("[启动] 应用程序初始化完成！")
        print("=" * 60)

    def show_disclaimer(self):
        """显示免责声明对话框"""
        disclaimer_dialog = DisclaimerDialog(self)
        result = disclaimer_dialog.exec()

        # 如果用户不同意，退出应用程序
        if result != QDialog.DialogCode.Accepted:
            print("用户不同意免责声明，应用程序将退出。")
            sys.exit(0)

    def setup_style(self):
        """设置应用程序全局样式"""
        # 设置应用程序字体
        app_font = QFont("Microsoft YaHei", 10)  # 使用微软雅黑字体，大小为10
        QApplication.setFont(app_font)

        # 设置全局样式表
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QWidget {
                font-family: 'Microsoft YaHei';
            }
            QGroupBox {
                border: 1px solid #cccccc;
                border-radius: 6px;
                margin-top: 12px;
                font-weight: bold;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
                background-color: #ffffff;
            }
            QPushButton {
                background-color: #2b579a;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #3a6bae;
            }
            QPushButton:pressed {
                background-color: #1c4377;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            QLineEdit, QComboBox {
                border: 1px solid #cccccc;
                border-radius: 4px;
                padding: 4px;
                background-color: #ffffff;
            }
            QLineEdit:focus, QComboBox:focus {
                border: 1px solid #2b579a;
            }
            QTableWidget {
                border: 1px solid #cccccc;
                border-radius: 4px;
                background-color: #ffffff;
                gridline-color: #e0e0e0;
                selection-background-color: #e6f0ff;
                selection-color: #000000;
            }
            QTableWidget::item {
                text-align: center;
                alignment: center;
            }
            QHeaderView::section {
                background-color: #f0f0f0;
                border: 1px solid #cccccc;
                padding: 4px;
                font-weight: bold;
                text-align: center;
            }
            QProgressBar {
                border: 1px solid #cccccc;
                border-radius: 4px;
                text-align: center;
                background-color: #ffffff;
            }
            QProgressBar::chunk {
                background-color: #2b579a;
                border-radius: 3px;
            }
            QCheckBox, QRadioButton {
                spacing: 8px;
            }
            QCheckBox::indicator, QRadioButton::indicator {
                width: 16px;
                height: 16px;
            }
            QSplitter::handle {
                background-color: #cccccc;
            }
            QStatusBar {
                background-color: #f0f0f0;
                color: #333333;
                border-top: 1px solid #cccccc;
            }
        """)

    def init_ui(self):
        """Initialize the user interface"""
        # Main widget and layout
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)  # 设置边距
        main_layout.setSpacing(10)  # 设置控件间距

        # 创建标题栏
        title_layout = QHBoxLayout()
        app_title = QLabel("华为/荣耀设备保修查询工具")
        app_title.setStyleSheet("font-size: 18px; font-weight: bold; color: #2b579a;")
        title_layout.addWidget(app_title)
        title_layout.addStretch()

        main_layout.addLayout(title_layout)

        # 创建品牌标签页
        self.brand_tabs = QTabWidget()
        self.brand_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #cccccc;
                border-radius: 6px;
                background-color: #f5f5f5;
            }
            QTabBar::tab {
                background-color: #e0e0e0;
                border: 1px solid #cccccc;
                border-bottom: none;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                padding: 8px 16px;
                margin-right: 2px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #f5f5f5;
                border-bottom: 1px solid #f5f5f5;
            }
            QTabBar::tab:hover:!selected {
                background-color: #e8e8e8;
            }
        """)

        # 创建荣耀标签页内容
        self.honor_tab = QWidget()
        self.create_honor_tab()
        self.brand_tabs.addTab(self.honor_tab, "荣耀设备查询")

        # 创建华为标签页内容
        self.huawei_tab = QWidget()
        self.create_huawei_tab()
        self.brand_tabs.addTab(self.huawei_tab, "华为设备查询")

        # 添加标签页到主布局
        main_layout.addWidget(self.brand_tabs)

        # Status bar
        self.status_bar = QStatusBar()
        self.status_bar.setFixedHeight(25)  # 固定状态栏高度
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪")

        # Set main widget
        self.setCentralWidget(main_widget)

    def create_honor_tab(self):
        """创建荣耀设备查询标签页"""
        # 创建标签页布局
        tab_layout = QVBoxLayout(self.honor_tab)

        # 创建左右分割器
        honor_splitter = QSplitter(Qt.Orientation.Horizontal)
        honor_splitter.setHandleWidth(2)
        honor_splitter.setChildrenCollapsible(False)

        # 创建左侧查询设置面板
        self.honor_query_panel = QWidget()
        self.create_honor_query_panel()

        # 创建右侧结果面板
        self.honor_results_panel = QWidget()
        self.create_honor_results_panel()

        # 添加面板到分割器
        honor_splitter.addWidget(self.honor_query_panel)
        honor_splitter.addWidget(self.honor_results_panel)

        # 设置初始大小 - 增加左侧面板宽度以确保UI元素正常显示
        honor_splitter.setSizes([400, 1000])

        # 设置左侧面板的最小宽度
        self.honor_query_panel.setMinimumWidth(380)

        # 添加分割器到标签页布局
        tab_layout.addWidget(honor_splitter)

    def create_huawei_tab(self):
        """创建华为设备查询标签页"""
        # 创建标签页布局
        tab_layout = QVBoxLayout(self.huawei_tab)

        # 创建左右分割器
        huawei_splitter = QSplitter(Qt.Orientation.Horizontal)
        huawei_splitter.setHandleWidth(2)
        huawei_splitter.setChildrenCollapsible(False)

        # 创建左侧查询设置面板
        self.huawei_query_panel = QWidget()
        self.create_huawei_query_panel()

        # 创建右侧结果面板
        self.huawei_results_panel = QWidget()
        self.create_huawei_results_panel()

        # 添加面板到分割器
        huawei_splitter.addWidget(self.huawei_query_panel)
        huawei_splitter.addWidget(self.huawei_results_panel)

        # 设置初始大小 - 增加左侧面板宽度以确保UI元素正常显示
        huawei_splitter.setSizes([400, 1000])

        # 设置左侧面板的最小宽度
        self.huawei_query_panel.setMinimumWidth(380)

        # 添加分割器到标签页布局
        tab_layout.addWidget(huawei_splitter)

    def create_honor_query_panel(self):
        """创建荣耀设备查询设置面板"""
        layout = QVBoxLayout(self.honor_query_panel)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Settings title
        title_label = QLabel("荣耀设备查询设置")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2b579a; padding-bottom: 5px;")
        layout.addWidget(title_label)

        # Input method group
        input_group = QGroupBox("序列号输入方式")
        input_layout = QVBoxLayout(input_group)
        input_layout.setContentsMargins(15, 20, 15, 15)
        input_layout.setSpacing(10)

        # Radio buttons for input method
        self.honor_input_method_group = QButtonGroup(self)
        self.honor_manual_input_radio = QRadioButton("手动输入")
        self.honor_excel_input_radio = QRadioButton("从Excel文件读取")
        self.honor_manual_input_radio.setChecked(True)

        self.honor_input_method_group.addButton(self.honor_manual_input_radio, 1)
        self.honor_input_method_group.addButton(self.honor_excel_input_radio, 2)

        radio_layout = QHBoxLayout()
        radio_layout.addWidget(self.honor_manual_input_radio)
        radio_layout.addWidget(self.honor_excel_input_radio)
        radio_layout.addStretch()
        input_layout.addLayout(radio_layout)

        # Manual input section
        self.honor_manual_input_widget = QWidget()
        manual_layout = QVBoxLayout(self.honor_manual_input_widget)
        manual_layout.setContentsMargins(0, 0, 0, 0)
        manual_layout.setSpacing(5)

        sn_label = QLabel("序列号:")
        sn_label.setStyleSheet("font-weight: bold;")
        manual_layout.addWidget(sn_label)

        self.honor_sn_input = QLineEdit()
        self.honor_sn_input.setPlaceholderText("输入设备序列号，多个请用英文逗号或空格分隔")
        self.honor_sn_input.setMinimumHeight(30)
        self.honor_sn_input.setMinimumWidth(300)  # 确保输入框有足够的宽度
        manual_layout.addWidget(self.honor_sn_input)

        input_layout.addWidget(self.honor_manual_input_widget)

        # Excel input section
        self.honor_excel_input_widget = QWidget()
        excel_layout = QVBoxLayout(self.honor_excel_input_widget)
        excel_layout.setContentsMargins(0, 0, 0, 0)
        excel_layout.setSpacing(5)

        file_label = QLabel("Excel文件:")
        file_label.setStyleSheet("font-weight: bold;")
        excel_layout.addWidget(file_label)

        file_select_layout = QHBoxLayout()
        self.honor_excel_path_input = QLineEdit()
        self.honor_excel_path_input.setReadOnly(True)
        self.honor_excel_path_input.setMinimumHeight(30)
        self.honor_excel_path_input.setMinimumWidth(250)  # 确保文件路径输入框有足够的宽度
        file_select_layout.addWidget(self.honor_excel_path_input)

        self.honor_browse_button = QPushButton("浏览...")
        self.honor_browse_button.setFixedWidth(80)
        file_select_layout.addWidget(self.honor_browse_button)
        excel_layout.addLayout(file_select_layout)

        column_label = QLabel("序列号列:")
        column_label.setStyleSheet("font-weight: bold;")
        excel_layout.addWidget(column_label)

        self.honor_excel_column_input = QComboBox()
        self.honor_excel_column_input.setMinimumHeight(30)
        excel_layout.addWidget(self.honor_excel_column_input)

        input_layout.addWidget(self.honor_excel_input_widget)
        layout.addWidget(input_group)

        # Proxy settings group
        proxy_group = QGroupBox("代理设置")
        proxy_layout = QVBoxLayout(proxy_group)
        proxy_layout.setContentsMargins(15, 20, 15, 15)
        proxy_layout.setSpacing(10)

        self.honor_use_proxy_checkbox = QCheckBox("启用代理进行查询")
        proxy_layout.addWidget(self.honor_use_proxy_checkbox)

        proxy_key_label = QLabel("代理API Key:")
        proxy_key_label.setStyleSheet("font-weight: bold;")
        proxy_layout.addWidget(proxy_key_label)

        self.honor_proxy_key_input = QLineEdit("0LM61IPB")  # Default key, user can customize
        self.honor_proxy_key_input.setMinimumHeight(30)
        self.honor_proxy_key_input.setPlaceholderText("请输入代理API密钥")
        self.honor_proxy_key_input.setToolTip("输入您的代理API密钥，您可以根据需要自定义")
        proxy_layout.addWidget(self.honor_proxy_key_input)

        # 添加提示信息
        proxy_hint_label = QLabel("💡 提示：您可以自定义代理API密钥")
        proxy_hint_label.setStyleSheet("color: #666666; font-size: 11px; margin-top: 5px;")
        proxy_layout.addWidget(proxy_hint_label)

        layout.addWidget(proxy_group)

        # Query settings group
        query_group = QGroupBox("查询设置")
        query_layout = QVBoxLayout(query_group)
        query_layout.setContentsMargins(15, 20, 15, 15)
        query_layout.setSpacing(10)

        concurrent_label = QLabel("并发查询数:")
        concurrent_label.setStyleSheet("font-weight: bold;")
        query_layout.addWidget(concurrent_label)

        self.honor_concurrent_input = QComboBox()
        self.honor_concurrent_input.addItems([
            "2 (推荐，稳定性最佳)",
            "4 (平衡性能和稳定性)",
            "8 (较高性能)",
            "16 (高性能)",
            "32 (最高性能，可能不稳定)"
        ])
        self.honor_concurrent_input.setCurrentIndex(0)  # Default to 2
        self.honor_concurrent_input.setMinimumHeight(30)
        query_layout.addWidget(self.honor_concurrent_input)

        # 添加并发提示标签
        self.honor_concurrent_tip_label = QLabel("提示: 当前使用低并发模式，查询速度较慢但稳定性最佳")
        self.honor_concurrent_tip_label.setStyleSheet("font-size: 10px; color: #666666; font-style: italic;")
        self.honor_concurrent_tip_label.setWordWrap(True)
        query_layout.addWidget(self.honor_concurrent_tip_label)

        layout.addWidget(query_group)

        # Buttons
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        self.honor_start_button = QPushButton("开始查询")
        self.honor_start_button.setMinimumHeight(40)
        self.honor_start_button.setIcon(QIcon.fromTheme("media-playback-start"))

        self.honor_stop_button = QPushButton("停止查询")
        self.honor_stop_button.setMinimumHeight(40)
        self.honor_stop_button.setIcon(QIcon.fromTheme("media-playback-stop"))
        self.honor_stop_button.setEnabled(False)

        button_layout.addWidget(self.honor_start_button)
        button_layout.addWidget(self.honor_stop_button)
        layout.addLayout(button_layout)

        # Connect signals
        self.honor_browse_button.clicked.connect(self.honor_browse_excel_file)
        self.honor_start_button.clicked.connect(self.honor_start_query)
        self.honor_stop_button.clicked.connect(self.honor_stop_query)
        self.honor_manual_input_radio.toggled.connect(self.honor_toggle_input_method)
        self.honor_excel_input_radio.toggled.connect(self.honor_toggle_input_method)
        self.honor_concurrent_input.currentTextChanged.connect(self.honor_update_concurrent_tip)

        # Initial state
        self.honor_toggle_input_method()

        layout.addStretch()

    def create_huawei_query_panel(self):
        """创建华为设备查询设置面板"""
        layout = QVBoxLayout(self.huawei_query_panel)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Settings title
        title_label = QLabel("华为设备查询设置")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2b579a; padding-bottom: 5px;")
        layout.addWidget(title_label)

        # Input method group
        input_group = QGroupBox("序列号输入方式")
        input_layout = QVBoxLayout(input_group)
        input_layout.setContentsMargins(15, 20, 15, 15)
        input_layout.setSpacing(10)

        # Radio buttons for input method
        self.huawei_input_method_group = QButtonGroup(self)
        self.huawei_manual_input_radio = QRadioButton("手动输入")
        self.huawei_excel_input_radio = QRadioButton("从Excel文件读取")
        self.huawei_manual_input_radio.setChecked(True)

        self.huawei_input_method_group.addButton(self.huawei_manual_input_radio, 1)
        self.huawei_input_method_group.addButton(self.huawei_excel_input_radio, 2)

        radio_layout = QHBoxLayout()
        radio_layout.addWidget(self.huawei_manual_input_radio)
        radio_layout.addWidget(self.huawei_excel_input_radio)
        radio_layout.addStretch()
        input_layout.addLayout(radio_layout)

        # Manual input section
        self.huawei_manual_input_widget = QWidget()
        manual_layout = QVBoxLayout(self.huawei_manual_input_widget)
        manual_layout.setContentsMargins(0, 0, 0, 0)
        manual_layout.setSpacing(5)

        sn_label = QLabel("序列号:")
        sn_label.setStyleSheet("font-weight: bold;")
        manual_layout.addWidget(sn_label)

        self.huawei_sn_input = QLineEdit()
        self.huawei_sn_input.setPlaceholderText("输入设备序列号，多个请用英文逗号或空格分隔")
        self.huawei_sn_input.setMinimumHeight(30)
        self.huawei_sn_input.setMinimumWidth(300)  # 确保输入框有足够的宽度
        manual_layout.addWidget(self.huawei_sn_input)

        input_layout.addWidget(self.huawei_manual_input_widget)

        # Excel input section
        self.huawei_excel_input_widget = QWidget()
        excel_layout = QVBoxLayout(self.huawei_excel_input_widget)
        excel_layout.setContentsMargins(0, 0, 0, 0)
        excel_layout.setSpacing(5)

        file_label = QLabel("Excel文件:")
        file_label.setStyleSheet("font-weight: bold;")
        excel_layout.addWidget(file_label)

        file_select_layout = QHBoxLayout()
        self.huawei_excel_path_input = QLineEdit()
        self.huawei_excel_path_input.setReadOnly(True)
        self.huawei_excel_path_input.setMinimumHeight(30)
        self.huawei_excel_path_input.setMinimumWidth(250)  # 确保文件路径输入框有足够的宽度
        file_select_layout.addWidget(self.huawei_excel_path_input)

        self.huawei_browse_button = QPushButton("浏览...")
        self.huawei_browse_button.setFixedWidth(80)
        file_select_layout.addWidget(self.huawei_browse_button)
        excel_layout.addLayout(file_select_layout)

        column_label = QLabel("序列号列:")
        column_label.setStyleSheet("font-weight: bold;")
        excel_layout.addWidget(column_label)

        self.huawei_excel_column_input = QComboBox()
        self.huawei_excel_column_input.setMinimumHeight(30)
        excel_layout.addWidget(self.huawei_excel_column_input)

        input_layout.addWidget(self.huawei_excel_input_widget)
        layout.addWidget(input_group)

        # Proxy settings group
        proxy_group = QGroupBox("代理设置")
        proxy_layout = QVBoxLayout(proxy_group)
        proxy_layout.setContentsMargins(15, 20, 15, 15)
        proxy_layout.setSpacing(10)

        self.huawei_use_proxy_checkbox = QCheckBox("启用代理进行查询")
        proxy_layout.addWidget(self.huawei_use_proxy_checkbox)

        proxy_key_label = QLabel("代理API Key:")
        proxy_key_label.setStyleSheet("font-weight: bold;")
        proxy_layout.addWidget(proxy_key_label)

        self.huawei_proxy_key_input = QLineEdit("0LM61IPB")  # Default key, user can customize
        self.huawei_proxy_key_input.setMinimumHeight(30)
        self.huawei_proxy_key_input.setPlaceholderText("请输入代理API密钥")
        self.huawei_proxy_key_input.setToolTip("输入您的代理API密钥，您可以根据需要自定义")
        proxy_layout.addWidget(self.huawei_proxy_key_input)

        # 添加提示信息
        proxy_hint_label = QLabel("💡 提示：您可以自定义代理API密钥")
        proxy_hint_label.setStyleSheet("color: #666666; font-size: 11px; margin-top: 5px;")
        proxy_layout.addWidget(proxy_hint_label)

        layout.addWidget(proxy_group)

        # Query settings group
        query_group = QGroupBox("查询设置")
        query_layout = QVBoxLayout(query_group)
        query_layout.setContentsMargins(15, 20, 15, 15)
        query_layout.setSpacing(10)

        concurrent_label = QLabel("并发查询数:")
        concurrent_label.setStyleSheet("font-weight: bold;")
        query_layout.addWidget(concurrent_label)

        self.huawei_concurrent_input = QComboBox()
        self.huawei_concurrent_input.addItems([
            "2 (推荐，稳定性最佳)",
            "4 (平衡性能和稳定性)",
            "8 (较高性能)",
            "16 (高性能)",
            "32 (最高性能，可能不稳定)"
        ])
        self.huawei_concurrent_input.setCurrentIndex(0)  # Default to 2
        self.huawei_concurrent_input.setMinimumHeight(30)
        query_layout.addWidget(self.huawei_concurrent_input)

        # 添加并发提示标签
        self.huawei_concurrent_tip_label = QLabel("提示: 当前使用低并发模式，查询速度较慢但稳定性最佳")
        self.huawei_concurrent_tip_label.setStyleSheet("font-size: 10px; color: #666666; font-style: italic;")
        self.huawei_concurrent_tip_label.setWordWrap(True)
        query_layout.addWidget(self.huawei_concurrent_tip_label)

        layout.addWidget(query_group)

        # Buttons
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        self.huawei_start_button = QPushButton("开始查询")
        self.huawei_start_button.setMinimumHeight(40)
        self.huawei_start_button.setIcon(QIcon.fromTheme("media-playback-start"))

        self.huawei_stop_button = QPushButton("停止查询")
        self.huawei_stop_button.setMinimumHeight(40)
        self.huawei_stop_button.setIcon(QIcon.fromTheme("media-playback-stop"))
        self.huawei_stop_button.setEnabled(False)

        button_layout.addWidget(self.huawei_start_button)
        button_layout.addWidget(self.huawei_stop_button)
        layout.addLayout(button_layout)

        # Connect signals
        self.huawei_browse_button.clicked.connect(self.huawei_browse_excel_file)
        self.huawei_start_button.clicked.connect(self.huawei_start_query)
        self.huawei_stop_button.clicked.connect(self.huawei_stop_query)
        self.huawei_manual_input_radio.toggled.connect(self.huawei_toggle_input_method)
        self.huawei_excel_input_radio.toggled.connect(self.huawei_toggle_input_method)
        self.huawei_concurrent_input.currentTextChanged.connect(self.huawei_update_concurrent_tip)

        # Initial state
        self.huawei_toggle_input_method()

        layout.addStretch()

    def create_honor_results_panel(self):
        """创建荣耀设备查询结果面板"""
        layout = QVBoxLayout(self.honor_results_panel)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Results header
        header_layout = QHBoxLayout()

        # Results title
        title_label = QLabel("荣耀设备查询结果")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2b579a; padding-bottom: 5px;")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # Retry all failed button
        self.honor_retry_all_button = QPushButton("一键重试")
        self.honor_retry_all_button.setIcon(QIcon.fromTheme("view-refresh"))
        self.honor_retry_all_button.setEnabled(False)
        self.honor_retry_all_button.setStyleSheet("""
            QPushButton {
                background-color: #ff6600;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e55a00;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        header_layout.addWidget(self.honor_retry_all_button)

        # Export button in header
        self.honor_export_button = QPushButton("导出结果")
        self.honor_export_button.setIcon(QIcon.fromTheme("document-save"))
        self.honor_export_button.setEnabled(False)
        header_layout.addWidget(self.honor_export_button)

        layout.addLayout(header_layout)

        # Results table with container
        table_container = QGroupBox()
        table_container.setStyleSheet("QGroupBox { border: 1px solid #cccccc; border-radius: 6px; background-color: #ffffff; }")
        table_layout = QVBoxLayout(table_container)
        table_layout.setContentsMargins(5, 5, 5, 5)

        self.honor_results_table = QTableWidget()
        self.honor_results_table.setColumnCount(10)
        self.honor_results_table.setHorizontalHeaderLabels([
            "序号", "序列号", "状态", "产品名称", "显示序列号",
            "适用范围", "查询时间", "耗时(秒)", "代理状态", "操作"
        ])

        # 设置表格样式
        self.honor_results_table.setAlternatingRowColors(True)  # 交替行颜色
        self.honor_results_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)  # 选择整行
        self.honor_results_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)  # 不可编辑

        # 隐藏垂直表头
        vertical_header = self.honor_results_table.verticalHeader()
        if vertical_header:
            vertical_header.hide()

        # 显示网格线
        self.honor_results_table.setShowGrid(True)

        # Set column widths
        self.honor_results_table.setColumnWidth(0, 50)   # 序号
        self.honor_results_table.setColumnWidth(1, 120)  # 序列号
        self.honor_results_table.setColumnWidth(2, 120)  # 状态
        self.honor_results_table.setColumnWidth(3, 180)  # 产品名称
        self.honor_results_table.setColumnWidth(4, 120)  # 显示序列号
        self.honor_results_table.setColumnWidth(5, 120)  # 适用范围
        self.honor_results_table.setColumnWidth(6, 150)  # 查询时间
        self.honor_results_table.setColumnWidth(7, 70)   # 耗时
        self.honor_results_table.setColumnWidth(8, 100)  # 代理状态
        self.honor_results_table.setColumnWidth(9, 70)   # 操作

        table_layout.addWidget(self.honor_results_table)
        layout.addWidget(table_container, 1)  # 表格容器占据大部分空间

        # Progress section
        progress_container = QGroupBox("查询进度")
        progress_container.setStyleSheet("""
            QGroupBox {
                border: 1px solid #cccccc;
                border-radius: 6px;
                margin-top: 12px;
                font-weight: bold;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
                background-color: #ffffff;
            }
        """)
        progress_layout = QVBoxLayout(progress_container)
        progress_layout.setContentsMargins(15, 20, 15, 15)

        # Progress bar with better styling
        self.honor_progress_bar = QProgressBar()
        self.honor_progress_bar.setMinimumHeight(25)
        self.honor_progress_bar.setTextVisible(True)
        self.honor_progress_bar.setFormat("%v/%m (%p%)")
        self.honor_progress_bar.setAlignment(Qt.AlignmentFlag.AlignCenter)
        progress_layout.addWidget(self.honor_progress_bar)

        # Add progress stats
        stats_layout = QHBoxLayout()
        stats_layout.addWidget(QLabel("总数: "))
        self.honor_total_count_label = QLabel("0")
        self.honor_total_count_label.setStyleSheet("font-weight: bold;")
        stats_layout.addWidget(self.honor_total_count_label)

        stats_layout.addSpacing(20)

        stats_layout.addWidget(QLabel("成功: "))
        self.honor_success_count_label = QLabel("0")
        self.honor_success_count_label.setStyleSheet("font-weight: bold; color: green;")
        stats_layout.addWidget(self.honor_success_count_label)

        stats_layout.addSpacing(20)

        stats_layout.addWidget(QLabel("失败: "))
        self.honor_failed_count_label = QLabel("0")
        self.honor_failed_count_label.setStyleSheet("font-weight: bold; color: red;")
        stats_layout.addWidget(self.honor_failed_count_label)

        stats_layout.addStretch()
        progress_layout.addLayout(stats_layout)

        layout.addWidget(progress_container)

        # Connect signals
        self.honor_export_button.clicked.connect(self.honor_export_results)
        self.honor_retry_all_button.clicked.connect(self.honor_retry_all_failed)

    def create_huawei_results_panel(self):
        """创建华为设备查询结果面板"""
        layout = QVBoxLayout(self.huawei_results_panel)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Results header
        header_layout = QHBoxLayout()

        # Results title
        title_label = QLabel("华为设备查询结果")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2b579a; padding-bottom: 5px;")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # Retry all failed button
        self.huawei_retry_all_button = QPushButton("一键重试")
        self.huawei_retry_all_button.setIcon(QIcon.fromTheme("view-refresh"))
        self.huawei_retry_all_button.setEnabled(False)
        self.huawei_retry_all_button.setStyleSheet("""
            QPushButton {
                background-color: #ff6600;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e55a00;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        header_layout.addWidget(self.huawei_retry_all_button)

        # Export button in header
        self.huawei_export_button = QPushButton("导出结果")
        self.huawei_export_button.setIcon(QIcon.fromTheme("document-save"))
        self.huawei_export_button.setEnabled(False)
        header_layout.addWidget(self.huawei_export_button)

        layout.addLayout(header_layout)

        # Results table with container
        table_container = QGroupBox()
        table_container.setStyleSheet("QGroupBox { border: 1px solid #cccccc; border-radius: 6px; background-color: #ffffff; }")
        table_layout = QVBoxLayout(table_container)
        table_layout.setContentsMargins(5, 5, 5, 5)

        self.huawei_results_table = QTableWidget()
        self.huawei_results_table.setColumnCount(10)
        self.huawei_results_table.setHorizontalHeaderLabels([
            "序号", "序列号", "状态", "产品名称", "显示序列号",
            "适用范围", "查询时间", "耗时(秒)", "代理状态", "操作"
        ])

        # 设置表格样式
        self.huawei_results_table.setAlternatingRowColors(True)  # 交替行颜色
        self.huawei_results_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)  # 选择整行
        self.huawei_results_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)  # 不可编辑

        # 隐藏垂直表头
        vertical_header = self.huawei_results_table.verticalHeader()
        if vertical_header:
            vertical_header.hide()

        # 显示网格线
        self.huawei_results_table.setShowGrid(True)

        # Set column widths
        self.huawei_results_table.setColumnWidth(0, 50)   # 序号
        self.huawei_results_table.setColumnWidth(1, 120)  # 序列号
        self.huawei_results_table.setColumnWidth(2, 120)  # 状态
        self.huawei_results_table.setColumnWidth(3, 180)  # 产品名称
        self.huawei_results_table.setColumnWidth(4, 120)  # 显示序列号
        self.huawei_results_table.setColumnWidth(5, 120)  # 适用范围
        self.huawei_results_table.setColumnWidth(6, 150)  # 查询时间
        self.huawei_results_table.setColumnWidth(7, 70)   # 耗时
        self.huawei_results_table.setColumnWidth(8, 100)  # 代理状态
        self.huawei_results_table.setColumnWidth(9, 70)   # 操作

        table_layout.addWidget(self.huawei_results_table)
        layout.addWidget(table_container, 1)  # 表格容器占据大部分空间

        # Progress section
        progress_container = QGroupBox("查询进度")
        progress_container.setStyleSheet("""
            QGroupBox {
                border: 1px solid #cccccc;
                border-radius: 6px;
                margin-top: 12px;
                font-weight: bold;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
                background-color: #ffffff;
            }
        """)
        progress_layout = QVBoxLayout(progress_container)
        progress_layout.setContentsMargins(15, 20, 15, 15)

        # Progress bar with better styling
        self.huawei_progress_bar = QProgressBar()
        self.huawei_progress_bar.setMinimumHeight(25)
        self.huawei_progress_bar.setTextVisible(True)
        self.huawei_progress_bar.setFormat("%v/%m (%p%)")
        self.huawei_progress_bar.setAlignment(Qt.AlignmentFlag.AlignCenter)
        progress_layout.addWidget(self.huawei_progress_bar)

        # Add progress stats
        stats_layout = QHBoxLayout()
        stats_layout.addWidget(QLabel("总数: "))
        self.huawei_total_count_label = QLabel("0")
        self.huawei_total_count_label.setStyleSheet("font-weight: bold;")
        stats_layout.addWidget(self.huawei_total_count_label)

        stats_layout.addSpacing(20)

        stats_layout.addWidget(QLabel("成功: "))
        self.huawei_success_count_label = QLabel("0")
        self.huawei_success_count_label.setStyleSheet("font-weight: bold; color: green;")
        stats_layout.addWidget(self.huawei_success_count_label)

        stats_layout.addSpacing(20)

        stats_layout.addWidget(QLabel("失败: "))
        self.huawei_failed_count_label = QLabel("0")
        self.huawei_failed_count_label.setStyleSheet("font-weight: bold; color: red;")
        stats_layout.addWidget(self.huawei_failed_count_label)

        stats_layout.addStretch()
        progress_layout.addLayout(stats_layout)

        layout.addWidget(progress_container)

        # Connect signals
        self.huawei_export_button.clicked.connect(self.huawei_export_results)
        self.huawei_retry_all_button.clicked.connect(self.huawei_retry_all_failed)

    # 荣耀设备查询相关方法
    def honor_toggle_input_method(self):
        """Toggle between manual input and Excel file input for Honor"""
        if self.honor_manual_input_radio.isChecked():
            self.honor_manual_input_widget.setEnabled(True)
            self.honor_excel_input_widget.setEnabled(False)
            self.honor_excel_column_input.setEnabled(False)
        else:
            self.honor_manual_input_widget.setEnabled(False)
            self.honor_excel_input_widget.setEnabled(True)
            self.honor_excel_column_input.setEnabled(True)

    def honor_browse_excel_file(self):
        """Open file dialog to select Excel file for Honor"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择Excel文件", "", "Excel Files (*.xlsx *.xls)"
        )
        if file_path:
            self.honor_excel_path_input.setText(file_path)
            self.honor_load_excel_columns(file_path)

    def honor_load_excel_columns(self, file_path):
        """Load column names from Excel file for Honor"""
        try:
            df = pd.read_excel(file_path, engine='openpyxl', header=0)
            self.honor_excel_column_input.clear()
            for column in df.columns:
                self.honor_excel_column_input.addItem(str(column))
            if not df.empty and len(df.columns) > 0:
                self.honor_excel_column_input.setCurrentIndex(0)
        except Exception as e:
            QMessageBox.warning(self, "Excel读取错误", f"无法读取Excel文件: {str(e)}")

    def honor_get_serial_numbers(self):
        """Get serial numbers from the selected input method for Honor"""
        print("\n" + "=" * 50)
        print("[序列号] 荣耀设备 - 开始获取序列号...")

        if self.honor_manual_input_radio.isChecked():
            print("[序列号] 输入方式: 手动输入")
            # Get from manual input
            sn_text = self.honor_sn_input.text().strip()
            if not sn_text:
                print("[错误] 手动输入为空")
                QMessageBox.warning(self, "输入错误", "请输入至少一个序列号")
                return []

            serial_numbers = [sn.strip() for sn in sn_text.replace(',', ' ').split() if sn.strip()]
            print(f"[序列号] 手动输入解析完成，获得 {len(serial_numbers)} 个序列号:")
            for i, sn in enumerate(serial_numbers, 1):
                print(f"   {i}. {sn}")
            return serial_numbers
        else:
            print("[序列号] 输入方式: Excel文件")
            # Get from Excel file
            file_path = self.honor_excel_path_input.text()
            if not file_path:
                print("[错误] 未选择Excel文件")
                QMessageBox.warning(self, "输入错误", "请选择Excel文件")
                return []

            print(f"[序列号] Excel文件路径: {file_path}")
            try:
                print("[序列号] 正在读取Excel文件...")
                df = pd.read_excel(file_path, engine='openpyxl', header=0)
                print(f"[序列号] Excel文件读取成功，共 {len(df)} 行数据")

                column = self.honor_excel_column_input.currentText()
                print(f"[序列号] 目标列名: {column}")

                if column not in df.columns:
                    print(f"[错误] 列 '{column}' 不存在")
                    print(f"[序列号] 可用列名: {list(df.columns)}")
                    QMessageBox.warning(self, "列错误", f"找不到列 '{column}'")
                    return []

                print(f"[序列号] 正在从列 '{column}' 提取序列号...")
                serial_numbers = df[column].astype(str).dropna().tolist()
                serial_numbers = [sn.strip() for sn in serial_numbers if sn.strip()]

                print(f"[序列号] Excel解析完成，获得 {len(serial_numbers)} 个有效序列号:")
                for i, sn in enumerate(serial_numbers[:10], 1):  # 只显示前10个
                    print(f"   {i}. {sn}")
                if len(serial_numbers) > 10:
                    print(f"   ... 还有 {len(serial_numbers) - 10} 个序列号")

                return serial_numbers
            except Exception as e:
                print(f"[错误] Excel读取失败: {str(e)}")
                QMessageBox.warning(self, "Excel读取错误", f"无法从Excel读取序列号: {str(e)}")
                return []

    def honor_start_query(self):
        """Start the Honor warranty query process"""
        print("\n" + "=" * 50)
        print("[查询] 荣耀设备保修查询启动")
        print("=" * 50)

        self.honor_serial_numbers = self.honor_get_serial_numbers()
        if not self.honor_serial_numbers:
            print("[错误] 未获取到有效序列号，查询终止")
            return

        print(f"[查询] 准备查询 {len(self.honor_serial_numbers)} 个荣耀设备序列号")

        # Update UI
        print("[查询] 更新UI状态...")
        self.honor_start_button.setEnabled(False)
        self.honor_stop_button.setEnabled(True)
        print("[查询] UI按钮状态更新完成")

        # Clear previous results
        print("[查询] 清理之前的查询结果...")
        self.honor_results_table.setRowCount(0)
        self.honor_results_table.setRowCount(len(self.honor_serial_numbers))
        self.honor_progress_bar.setMaximum(len(self.honor_serial_numbers))
        self.honor_progress_bar.setValue(0)
        print("[查询] 结果表格已重置")

        # 重置统计数据
        print("[查询] 重置统计数据...")
        self.honor_total_count_label.setText(str(len(self.honor_serial_numbers)))
        self.honor_success_count_label.setText("0")
        self.honor_failed_count_label.setText("0")
        self.honor_success_count = 0
        self.honor_failed_count = 0
        print("[查询] 统计数据重置完成")

        # Initialize table with serial numbers
        print("[查询] 初始化结果表格...")
        for i, sn in enumerate(self.honor_serial_numbers):
            # 序号
            index_item = QTableWidgetItem(str(i+1))
            index_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.honor_results_table.setItem(i, 0, index_item)

            # 序列号
            sn_item = QTableWidgetItem(sn)
            sn_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.honor_results_table.setItem(i, 1, sn_item)

            # 状态
            status_item = QTableWidgetItem("等待查询")
            status_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            status_item.setForeground(QColor("#666666"))  # 灰色文字
            self.honor_results_table.setItem(i, 2, status_item)
        print(f"[查询] 表格初始化完成，已添加 {len(self.honor_serial_numbers)} 行数据")

        # Create and start worker thread
        use_proxy = self.honor_use_proxy_checkbox.isChecked()
        proxy_key = self.honor_proxy_key_input.text() if use_proxy else ""

        # 解析并发数量（从文本中提取数字）
        concurrent_text = self.honor_concurrent_input.currentText()
        concurrent_queries = int(concurrent_text.split()[0])  # 提取第一个数字

        print(f"[配置] 查询配置:")
        print(f"   使用代理: {'是' if use_proxy else '否'}")
        if use_proxy:
            print(f"   代理密钥: {proxy_key[:8]}..." if len(proxy_key) > 8 else f"   代理密钥: {proxy_key}")
        print(f"   并发数量: {concurrent_queries}")

        print("[查询] 创建查询工作线程...")
        self.honor_query_worker = WarrantyQueryWorker(self.honor_serial_numbers, use_proxy, proxy_key)
        self.honor_query_worker.max_concurrent_queries = concurrent_queries

        # Connect signals
        print("[查询] 连接信号槽...")
        self.honor_query_worker.progress_updated.connect(self.honor_update_progress)
        self.honor_query_worker.query_completed.connect(self.honor_update_result)
        self.honor_query_worker.all_completed.connect(self.honor_queries_completed)
        print("[查询] 信号槽连接完成")

        # Start worker
        print("[查询] 启动查询工作线程...")
        self.honor_query_worker.start()
        print("[查询] 查询工作线程已启动")

        status_message = f"正在查询 {len(self.honor_serial_numbers)} 个荣耀设备序列号..."
        self.status_bar.showMessage(status_message)
        print(f"[状态] {status_message}")
        print("=" * 50)

    def honor_stop_query(self):
        """Stop the Honor query process"""
        if self.honor_query_worker and self.honor_query_worker.isRunning():
            self.honor_query_worker.stop()
            self.status_bar.showMessage("正在停止荣耀设备查询...")

    def honor_update_progress(self, row_index, status):
        """Update the progress of a specific Honor query"""
        if 0 <= row_index < self.honor_results_table.rowCount():
            status_item = QTableWidgetItem(status)
            status_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            status_item.setForeground(QColor("#0066cc"))  # 蓝色表示正在进行
            self.honor_results_table.setItem(row_index, 2, status_item)

    def honor_update_result(self, row_index, result):
        """Update the result of a specific Honor query"""
        if 0 <= row_index < self.honor_results_table.rowCount():
            sn = self.honor_serial_numbers[row_index] if row_index < len(self.honor_serial_numbers) else "未知"
            print(f"[结果] 荣耀设备查询结果更新 - 序号: {row_index + 1}, SN: {sn}")

            # Update status with color
            status = result["status"]
            duration = result.get('duration_seconds', 0)
            proxy_used = result.get("proxy_used", "未启用")

            print(f"   查询状态: {status}")
            print(f"   查询耗时: {duration:.1f}秒")
            print(f"   代理状态: {proxy_used}")

            status_item = QTableWidgetItem(status)
            status_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)

            # 根据状态设置不同的颜色
            if status == "成功":
                status_item.setForeground(QColor("#008800"))  # 绿色表示成功
                self.honor_success_count += 1
                self.honor_success_count_label.setText(str(self.honor_success_count))
                print(f"   [成功] 查询成功！当前成功数: {self.honor_success_count}")

                # 打印产品信息
                if result.get("data") and result["data"].get("产品信息"):
                    product_info = result["data"]["产品信息"]
                    print(f"   产品名称: {product_info.get('产品名称', '未知')}")
                    print(f"   显示序列号: {product_info.get('显示序列号', '未知')}")
                    print(f"   适用范围: {product_info.get('适用范围', '未知')}")

            elif "失败" in status or "错误" in status:
                status_item.setForeground(QColor("#cc0000"))  # 红色表示失败
                self.honor_failed_count += 1
                self.honor_failed_count_label.setText(str(self.honor_failed_count))
                print(f"   [失败] 查询失败！当前失败数: {self.honor_failed_count}")
            else:
                status_item.setForeground(QColor("#ff6600"))  # 橙色表示其他状态
                self.honor_failed_count += 1
                self.honor_failed_count_label.setText(str(self.honor_failed_count))
                print(f"   [异常] 查询异常！状态: {status}, 当前失败数: {self.honor_failed_count}")

            self.honor_results_table.setItem(row_index, 2, status_item)

            # Update product info if available
            if result.get("data") and result["data"].get("产品信息"):
                product_info = result["data"]["产品信息"]

                # 创建居中对齐的单元格项
                product_name_item = QTableWidgetItem(product_info.get("产品名称", ""))
                product_name_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.honor_results_table.setItem(row_index, 3, product_name_item)

                sn_item = QTableWidgetItem(product_info.get("显示序列号", ""))
                sn_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.honor_results_table.setItem(row_index, 4, sn_item)

                region_item = QTableWidgetItem(product_info.get("适用范围", ""))
                region_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.honor_results_table.setItem(row_index, 5, region_item)

            # Update query time
            current_time = time.strftime("%Y-%m-%d %H:%M:%S")
            time_item = QTableWidgetItem(current_time)
            time_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.honor_results_table.setItem(row_index, 6, time_item)

            # Update duration with formatting
            duration_item = QTableWidgetItem(f"{result['duration_seconds']:.1f}")
            duration_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.honor_results_table.setItem(row_index, 7, duration_item)

            # Update proxy status
            proxy_item = QTableWidgetItem(result.get("proxy_used", "未启用"))
            proxy_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.honor_results_table.setItem(row_index, 8, proxy_item)

            # Add action button based on status
            if status == "成功" and result.get("data"):
                # 成功状态显示"已完成"按钮
                action_button = QPushButton("已完成")
                action_button.setStyleSheet("""
                    QPushButton {
                        font-size: 12px;
                        padding: 2px 8px;
                        background-color: #28a745;
                        color: white;
                        border: none;
                        border-radius: 3px;
                    }
                    QPushButton:hover {
                        background-color: #218838;
                    }
                """)
                action_button.setEnabled(False)  # 已完成的不可点击
                self.honor_results_table.setCellWidget(row_index, 9, action_button)
            elif status != "成功" and status != "等待查询" and status != "正在查询...":
                # 失败状态显示"重试"按钮
                retry_button = QPushButton("重试")
                retry_button.setStyleSheet("""
                    QPushButton {
                        font-size: 12px;
                        padding: 2px 8px;
                        background-color: #dc3545;
                        color: white;
                        border: none;
                        border-radius: 3px;
                    }
                    QPushButton:hover {
                        background-color: #c82333;
                    }
                """)
                # 连接重试点击事件
                retry_button.clicked.connect(lambda _, idx=row_index: self.honor_retry_single(idx))
                self.honor_results_table.setCellWidget(row_index, 9, retry_button)

            # Update progress bar
            self.honor_progress_bar.setValue(self.honor_progress_bar.value() + 1)

    def honor_queries_completed(self):
        """Handle completion of all Honor queries"""
        print("\n" + "=" * 50)
        print("[完成] 荣耀设备查询全部完成")
        print("=" * 50)

        # 显示详细的完成信息
        total = len(self.honor_serial_numbers)
        success = self.honor_success_count
        failed = self.honor_failed_count
        success_rate = (success / total * 100) if total > 0 else 0

        print(f"[统计] 查询统计结果:")
        print(f"   总查询数: {total}")
        print(f"   成功数量: {success}")
        print(f"   失败数量: {failed}")
        print(f"   成功率: {success_rate:.1f}%")

        # Update UI state
        print("[完成] 更新UI状态...")
        self.honor_start_button.setEnabled(True)
        self.honor_stop_button.setEnabled(False)
        self.honor_export_button.setEnabled(True)

        # 检查是否有失败的查询，如果有则启用一键重试按钮
        if failed > 0:
            self.honor_retry_all_button.setEnabled(True)
        else:
            self.honor_retry_all_button.setEnabled(False)
        print("[完成] UI状态更新完成")

        status_message = f"荣耀设备查询完成 | 总数: {total} | 成功: {success} | 失败: {failed} | 成功率: {success_rate:.1f}%"
        self.status_bar.showMessage(status_message)
        print(f"[状态] {status_message}")

        # 如果有成功的查询，弹出提示
        if success > 0:
            print("[完成] 显示完成提示对话框...")
            QMessageBox.information(
                self,
                "查询完成",
                f"荣耀设备查询已完成！\n\n总数: {total}\n成功: {success}\n失败: {failed}\n成功率: {success_rate:.1f}%\n\n您可以点击\"导出结果\"按钮导出查询结果。"
            )
        else:
            print("[警告] 所有查询都失败了，不显示成功提示")

        # Clean up worker
        print("[完成] 清理查询工作线程...")
        self.honor_query_worker = None
        print("[完成] 查询工作线程已清理")
        print("=" * 50)

    def honor_export_results(self):
        """Export Honor results to Excel file"""
        print("\n" + "=" * 40)
        print("[导出] 荣耀设备查询结果导出")
        print("=" * 40)

        if self.honor_results_table.rowCount() == 0:
            print("[错误] 没有可导出的结果")
            QMessageBox.information(self, "导出提示", "没有可导出的结果")
            return

        print(f"[导出] 准备导出 {self.honor_results_table.rowCount()} 行查询结果")

        default_filename = f"honor_query_results_{time.strftime('%Y%m%d_%H%M%S')}.xlsx"
        print(f"[导出] 默认文件名: {default_filename}")

        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出结果", default_filename,
            "Excel Files (*.xlsx)"
        )

        if not file_path:
            print("[导出] 用户取消了文件选择")
            return

        print(f"[导出] 选择的导出路径: {file_path}")

        try:
            print("[导出] 开始处理表格数据...")
            # Create DataFrame from table data
            data = []
            for row in range(self.honor_results_table.rowCount()):
                row_data = {}
                for col in range(self.honor_results_table.columnCount() - 1):  # Skip the "操作" column
                    header_item = self.honor_results_table.horizontalHeaderItem(col)
                    header = header_item.text() if header_item else f"列{col}"
                    item = self.honor_results_table.item(row, col)
                    row_data[header] = item.text() if item else ""
                data.append(row_data)

            print(f"[导出] 表格数据处理完成，共 {len(data)} 行数据")

            print("[导出] 正在写入Excel文件...")
            df = pd.DataFrame(data)
            df.to_excel(file_path, index=False)
            print("[导出] Excel文件写入完成")

            print(f"[导出] 导出成功！文件路径: {file_path}")
            QMessageBox.information(self, "导出成功", f"结果已成功导出到: {file_path}")
        except Exception as e:
            print(f"[错误] 导出失败: {str(e)}")
            QMessageBox.warning(self, "导出错误", f"导出结果时出错: {str(e)}")

        print("=" * 40)

    # 华为设备查询相关方法
    def huawei_toggle_input_method(self):
        """Toggle between manual input and Excel file input for Huawei"""
        if self.huawei_manual_input_radio.isChecked():
            self.huawei_manual_input_widget.setEnabled(True)
            self.huawei_excel_input_widget.setEnabled(False)
            self.huawei_excel_column_input.setEnabled(False)
        else:
            self.huawei_manual_input_widget.setEnabled(False)
            self.huawei_excel_input_widget.setEnabled(True)
            self.huawei_excel_column_input.setEnabled(True)

    def huawei_browse_excel_file(self):
        """Open file dialog to select Excel file for Huawei"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择Excel文件", "", "Excel Files (*.xlsx *.xls)"
        )
        if file_path:
            self.huawei_excel_path_input.setText(file_path)
            self.huawei_load_excel_columns(file_path)

    def huawei_load_excel_columns(self, file_path):
        """Load column names from Excel file for Huawei"""
        try:
            df = pd.read_excel(file_path, engine='openpyxl', header=0)
            self.huawei_excel_column_input.clear()
            for column in df.columns:
                self.huawei_excel_column_input.addItem(str(column))
            if not df.empty and len(df.columns) > 0:
                self.huawei_excel_column_input.setCurrentIndex(0)
        except Exception as e:
            QMessageBox.warning(self, "Excel读取错误", f"无法读取Excel文件: {str(e)}")

    def huawei_get_serial_numbers(self):
        """Get serial numbers from the selected input method for Huawei"""
        if self.huawei_manual_input_radio.isChecked():
            # Get from manual input
            sn_text = self.huawei_sn_input.text().strip()
            if not sn_text:
                QMessageBox.warning(self, "输入错误", "请输入至少一个序列号")
                return []
            return [sn.strip() for sn in sn_text.replace(',', ' ').split() if sn.strip()]
        else:
            # Get from Excel file
            file_path = self.huawei_excel_path_input.text()
            if not file_path:
                QMessageBox.warning(self, "输入错误", "请选择Excel文件")
                return []

            try:
                df = pd.read_excel(file_path, engine='openpyxl', header=0)
                column = self.huawei_excel_column_input.currentText()
                if column not in df.columns:
                    QMessageBox.warning(self, "列错误", f"找不到列 '{column}'")
                    return []

                serial_numbers = df[column].astype(str).dropna().tolist()
                return [sn.strip() for sn in serial_numbers if sn.strip()]
            except Exception as e:
                QMessageBox.warning(self, "Excel读取错误", f"无法从Excel读取序列号: {str(e)}")
                return []

    def huawei_start_query(self):
        """Start the Huawei warranty query process"""
        self.huawei_serial_numbers = self.huawei_get_serial_numbers()
        if not self.huawei_serial_numbers:
            return

        # Update UI
        self.huawei_start_button.setEnabled(False)
        self.huawei_stop_button.setEnabled(True)

        # Clear previous results
        self.huawei_results_table.setRowCount(0)
        self.huawei_results_table.setRowCount(len(self.huawei_serial_numbers))
        self.huawei_progress_bar.setMaximum(len(self.huawei_serial_numbers))
        self.huawei_progress_bar.setValue(0)

        # 重置统计数据
        self.huawei_total_count_label.setText(str(len(self.huawei_serial_numbers)))
        self.huawei_success_count_label.setText("0")
        self.huawei_failed_count_label.setText("0")
        self.huawei_success_count = 0
        self.huawei_failed_count = 0

        # Initialize table with serial numbers
        for i, sn in enumerate(self.huawei_serial_numbers):
            # 序号
            index_item = QTableWidgetItem(str(i+1))
            index_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.huawei_results_table.setItem(i, 0, index_item)

            # 序列号
            sn_item = QTableWidgetItem(sn)
            sn_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.huawei_results_table.setItem(i, 1, sn_item)

            # 状态
            status_item = QTableWidgetItem("等待查询")
            status_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            status_item.setForeground(QColor("#666666"))  # 灰色文字
            self.huawei_results_table.setItem(i, 2, status_item)

        # Create and start worker thread
        use_proxy = self.huawei_use_proxy_checkbox.isChecked()
        proxy_key = self.huawei_proxy_key_input.text() if use_proxy else ""

        # 解析并发数量（从文本中提取数字）
        concurrent_text = self.huawei_concurrent_input.currentText()
        concurrent_queries = int(concurrent_text.split()[0])  # 提取第一个数字

        self.huawei_query_worker = HuaweiWarrantyQueryWorker(self.huawei_serial_numbers, use_proxy, proxy_key)
        self.huawei_query_worker.max_concurrent_queries = concurrent_queries

        # Connect signals
        self.huawei_query_worker.progress_updated.connect(self.huawei_update_progress)
        self.huawei_query_worker.query_completed.connect(self.huawei_update_result)
        self.huawei_query_worker.all_completed.connect(self.huawei_queries_completed)

        # Start worker
        self.huawei_query_worker.start()

        self.status_bar.showMessage(f"正在查询 {len(self.huawei_serial_numbers)} 个华为设备序列号...")

    def huawei_stop_query(self):
        """Stop the Huawei query process"""
        if self.huawei_query_worker and self.huawei_query_worker.isRunning():
            self.huawei_query_worker.stop()
            self.status_bar.showMessage("正在停止华为设备查询...")

    def huawei_update_progress(self, row_index, status):
        """Update the progress of a specific Huawei query"""
        if 0 <= row_index < self.huawei_results_table.rowCount():
            status_item = QTableWidgetItem(status)
            status_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            status_item.setForeground(QColor("#0066cc"))  # 蓝色表示正在进行
            self.huawei_results_table.setItem(row_index, 2, status_item)

    def huawei_update_result(self, row_index, result):
        """Update the result of a specific Huawei query"""
        if 0 <= row_index < self.huawei_results_table.rowCount():
            # Update status with color
            status = result["status"]

            # 将"ok"状态改为"成功"
            if status == "ok":
                status = "成功"

            status_item = QTableWidgetItem(status)
            status_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)

            # 根据状态设置不同的颜色
            if status == "成功":
                status_item.setForeground(QColor("#008800"))  # 绿色表示成功
                self.huawei_success_count += 1
                self.huawei_success_count_label.setText(str(self.huawei_success_count))
            elif status == "序列号错误":
                status_item.setForeground(QColor("#ff6600"))  # 橙色表示序列号错误
                self.huawei_failed_count += 1
                self.huawei_failed_count_label.setText(str(self.huawei_failed_count))
                # 在状态栏显示简短的错误信息
                sn = self.huawei_serial_numbers[row_index] if row_index < len(self.huawei_serial_numbers) else "未知"
                error_msg = result.get("error_message", "设备序列号错误")
                self.status_bar.showMessage(f"SN {sn}: {error_msg}")
            elif status == "设备序列号无效":
                status_item.setForeground(QColor("#ff6600"))  # 橙色表示设备序列号无效
                self.huawei_failed_count += 1
                self.huawei_failed_count_label.setText(str(self.huawei_failed_count))
                # 在状态栏显示简短的错误信息
                sn = self.huawei_serial_numbers[row_index] if row_index < len(self.huawei_serial_numbers) else "未知"
                error_msg = result.get("error_message", "设备序列号无效")
                self.status_bar.showMessage(f"SN {sn}: {error_msg}")
            elif "失败" in status or "错误" in status:
                status_item.setForeground(QColor("#cc0000"))  # 红色表示失败
                self.huawei_failed_count += 1
                self.huawei_failed_count_label.setText(str(self.huawei_failed_count))
            else:
                status_item.setForeground(QColor("#ff6600"))  # 橙色表示其他状态
                self.huawei_failed_count += 1
                self.huawei_failed_count_label.setText(str(self.huawei_failed_count))

            self.huawei_results_table.setItem(row_index, 2, status_item)

            # Update product info if available
            if result.get("data") and result["data"].get("product_info"):
                product_info = result["data"]["product_info"]

                # 创建居中对齐的单元格项
                product_name_item = QTableWidgetItem(product_info.get("name", ""))
                product_name_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.huawei_results_table.setItem(row_index, 3, product_name_item)

                sn_item = QTableWidgetItem(product_info.get("serial_number", ""))
                sn_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.huawei_results_table.setItem(row_index, 4, sn_item)

                region_item = QTableWidgetItem(product_info.get("region", ""))
                region_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.huawei_results_table.setItem(row_index, 5, region_item)

            # Update query time
            current_time = time.strftime("%Y-%m-%d %H:%M:%S")
            time_item = QTableWidgetItem(current_time)
            time_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.huawei_results_table.setItem(row_index, 6, time_item)

            # Update duration with formatting
            duration_item = QTableWidgetItem(f"{result['duration_seconds']:.1f}")
            duration_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.huawei_results_table.setItem(row_index, 7, duration_item)

            # Update proxy status
            proxy_item = QTableWidgetItem(result.get("proxy_used", "未启用"))
            proxy_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.huawei_results_table.setItem(row_index, 8, proxy_item)

            # Add action button based on status
            if status == "成功" and result.get("data"):
                # 成功状态显示"已完成"按钮
                action_button = QPushButton("已完成")
                action_button.setStyleSheet("""
                    QPushButton {
                        font-size: 12px;
                        padding: 2px 8px;
                        background-color: #28a745;
                        color: white;
                        border: none;
                        border-radius: 3px;
                    }
                    QPushButton:hover {
                        background-color: #218838;
                    }
                """)
                action_button.setEnabled(False)  # 已完成的不可点击
                self.huawei_results_table.setCellWidget(row_index, 9, action_button)
            elif status != "成功" and status != "等待查询" and status != "正在查询...":
                # 失败状态显示"重试"按钮
                retry_button = QPushButton("重试")
                retry_button.setStyleSheet("""
                    QPushButton {
                        font-size: 12px;
                        padding: 2px 8px;
                        background-color: #dc3545;
                        color: white;
                        border: none;
                        border-radius: 3px;
                    }
                    QPushButton:hover {
                        background-color: #c82333;
                    }
                """)
                # 连接重试点击事件
                retry_button.clicked.connect(lambda _, idx=row_index: self.huawei_retry_single(idx))
                self.huawei_results_table.setCellWidget(row_index, 9, retry_button)

            # Update progress bar
            self.huawei_progress_bar.setValue(self.huawei_progress_bar.value() + 1)

    def huawei_queries_completed(self):
        """Handle completion of all Huawei queries"""
        self.huawei_start_button.setEnabled(True)
        self.huawei_stop_button.setEnabled(False)
        self.huawei_export_button.setEnabled(True)

        # 检查是否有失败的查询，如果有则启用一键重试按钮
        failed_count = self.huawei_failed_count
        if failed_count > 0:
            self.huawei_retry_all_button.setEnabled(True)
        else:
            self.huawei_retry_all_button.setEnabled(False)

        # 显示详细的完成信息
        total = len(self.huawei_serial_numbers)
        success = self.huawei_success_count
        failed = self.huawei_failed_count
        success_rate = (success / total * 100) if total > 0 else 0

        self.status_bar.showMessage(
            f"华为设备查询完成 | 总数: {total} | 成功: {success} | 失败: {failed} | 成功率: {success_rate:.1f}%"
        )

        # 如果有成功的查询，弹出提示
        if success > 0:
            QMessageBox.information(
                self,
                "查询完成",
                f"华为设备查询已完成！\n\n总数: {total}\n成功: {success}\n失败: {failed}\n成功率: {success_rate:.1f}%\n\n您可以点击\"导出结果\"按钮导出查询结果。"
            )

        # Clean up worker
        self.huawei_query_worker = None

    def honor_retry_single(self, row_index):
        """重试单个荣耀设备查询"""
        if not hasattr(self, 'honor_serial_numbers') or row_index >= len(self.honor_serial_numbers):
            print(f"[错误] 无效的行索引: {row_index}")
            return

        # 检查是否有查询正在进行
        if hasattr(self, 'honor_query_worker') and self.honor_query_worker and self.honor_query_worker.isRunning():
            QMessageBox.warning(self, "重试提示", "当前有查询任务正在进行，请等待完成后再重试")
            return

        serial_number = self.honor_serial_numbers[row_index]
        print(f"[重试] 开始重试荣耀设备查询 - 序号: {row_index + 1}, SN: {serial_number}")

        # 更新状态为正在查询
        status_item = QTableWidgetItem("正在重试...")
        status_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        status_item.setForeground(QColor("#0066cc"))  # 蓝色表示正在进行
        self.honor_results_table.setItem(row_index, 2, status_item)

        # 清除操作列的按钮
        self.honor_results_table.setCellWidget(row_index, 9, None)

        # 创建单个查询的工作线程
        use_proxy = self.honor_use_proxy_checkbox.isChecked()
        proxy_key = self.honor_proxy_key_input.text() if use_proxy else ""

        self.honor_single_retry_worker = WarrantyQueryWorker([serial_number], use_proxy, proxy_key)
        self.honor_single_retry_worker.max_concurrent_queries = 1

        # 连接信号，但需要调整行索引
        self.honor_single_retry_worker.query_completed.connect(
            lambda _, result: self.honor_update_result(row_index, result)
        )

        # 启动工作线程
        self.honor_single_retry_worker.start()

        self.status_bar.showMessage(f"正在重试荣耀设备序列号: {serial_number}")

    def honor_retry_all_failed(self):
        """一键重试所有失败的荣耀设备查询"""
        print("[重试] 开始一键重试所有失败的荣耀设备查询")

        # 检查是否有查询正在进行
        if hasattr(self, 'honor_query_worker') and self.honor_query_worker and self.honor_query_worker.isRunning():
            QMessageBox.warning(self, "重试提示", "当前有查询任务正在进行，请等待完成后再重试")
            return

        if not hasattr(self, 'honor_serial_numbers'):
            QMessageBox.warning(self, "重试提示", "没有可重试的查询记录")
            return

        # 收集所有失败的序列号和对应的行索引
        failed_items = []
        for row in range(self.honor_results_table.rowCount()):
            status_item = self.honor_results_table.item(row, 2)
            if status_item:
                status = status_item.text()
                if status != "成功" and status != "等待查询" and status != "正在查询...":
                    if row < len(self.honor_serial_numbers):
                        failed_items.append((row, self.honor_serial_numbers[row]))

        if not failed_items:
            QMessageBox.information(self, "重试提示", "没有失败的查询需要重试")
            return

        # 确认重试
        reply = QMessageBox.question(
            self, "确认重试",
            f"确定要重试 {len(failed_items)} 个失败的荣耀设备查询吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.Yes
        )

        if reply != QMessageBox.StandardButton.Yes:
            return

        print(f"[重试] 准备重试 {len(failed_items)} 个失败的查询")

        # 禁用重试按钮
        self.honor_retry_all_button.setEnabled(False)
        self.honor_start_button.setEnabled(False)

        # 更新失败项的状态
        for row_index, _ in failed_items:
            status_item = QTableWidgetItem("等待重试...")
            status_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            status_item.setForeground(QColor("#0066cc"))
            self.honor_results_table.setItem(row_index, 2, status_item)

            # 清除操作列的按钮
            self.honor_results_table.setCellWidget(row_index, 9, None)

        # 创建重试查询的工作线程
        use_proxy = self.honor_use_proxy_checkbox.isChecked()
        proxy_key = self.honor_proxy_key_input.text() if use_proxy else ""

        # 解析并发数量
        concurrent_text = self.honor_concurrent_input.currentText()
        concurrent_queries = int(concurrent_text.split()[0])

        # 只重试失败的序列号
        retry_serial_numbers = [sn for _, sn in failed_items]
        self.honor_retry_row_mapping = {i: failed_items[i][0] for i in range(len(failed_items))}

        self.honor_retry_worker = WarrantyQueryWorker(retry_serial_numbers, use_proxy, proxy_key)
        self.honor_retry_worker.max_concurrent_queries = concurrent_queries

        # 连接信号
        self.honor_retry_worker.progress_updated.connect(self.honor_retry_update_progress)
        self.honor_retry_worker.query_completed.connect(self.honor_retry_update_result)
        self.honor_retry_worker.all_completed.connect(self.honor_retry_completed)

        # 启动工作线程
        self.honor_retry_worker.start()

        self.status_bar.showMessage(f"正在重试 {len(retry_serial_numbers)} 个失败的荣耀设备序列号...")

    def honor_retry_update_progress(self, worker_row_index, status):
        """更新重试查询的进度"""
        if hasattr(self, 'honor_retry_row_mapping') and worker_row_index in self.honor_retry_row_mapping:
            actual_row_index = self.honor_retry_row_mapping[worker_row_index]
            if 0 <= actual_row_index < self.honor_results_table.rowCount():
                status_item = QTableWidgetItem(status)
                status_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                status_item.setForeground(QColor("#0066cc"))
                self.honor_results_table.setItem(actual_row_index, 2, status_item)

    def honor_retry_update_result(self, worker_row_index, result):
        """更新重试查询的结果"""
        if hasattr(self, 'honor_retry_row_mapping') and worker_row_index in self.honor_retry_row_mapping:
            actual_row_index = self.honor_retry_row_mapping[worker_row_index]

            # 如果重试成功，需要更新统计数据
            old_status_item = self.honor_results_table.item(actual_row_index, 2)
            old_status = old_status_item.text() if old_status_item else ""

            # 调用原有的结果更新方法
            self.honor_update_result(actual_row_index, result)

            # 更新统计数据（因为原来是失败的，现在可能成功了）
            new_status = result["status"]
            if new_status == "成功" and old_status != "成功":
                # 从失败转为成功
                if self.honor_failed_count > 0:
                    self.honor_failed_count -= 1
                    self.honor_failed_count_label.setText(str(self.honor_failed_count))

    def honor_retry_completed(self):
        """重试查询完成"""
        print("[重试] 荣耀设备重试查询全部完成")

        # 重新启用按钮
        self.honor_start_button.setEnabled(True)

        # 检查是否还有失败的查询
        failed_count = 0
        for row in range(self.honor_results_table.rowCount()):
            status_item = self.honor_results_table.item(row, 2)
            if status_item:
                status = status_item.text()
                if status != "成功" and status != "等待查询" and status != "正在查询...":
                    failed_count += 1

        if failed_count > 0:
            self.honor_retry_all_button.setEnabled(True)
        else:
            self.honor_retry_all_button.setEnabled(False)

        # 清理工作线程
        self.honor_retry_worker = None
        if hasattr(self, 'honor_retry_row_mapping'):
            delattr(self, 'honor_retry_row_mapping')

        # 更新状态栏
        total = len(self.honor_serial_numbers)
        success = self.honor_success_count
        failed = self.honor_failed_count
        success_rate = (success / total * 100) if total > 0 else 0

        self.status_bar.showMessage(
            f"荣耀设备重试完成 | 总数: {total} | 成功: {success} | 失败: {failed} | 成功率: {success_rate:.1f}%"
        )

    def huawei_retry_single(self, row_index):
        """重试单个华为设备查询"""
        if not hasattr(self, 'huawei_serial_numbers') or row_index >= len(self.huawei_serial_numbers):
            print(f"[错误] 无效的行索引: {row_index}")
            return

        # 检查是否有查询正在进行
        if hasattr(self, 'huawei_query_worker') and self.huawei_query_worker and self.huawei_query_worker.isRunning():
            QMessageBox.warning(self, "重试提示", "当前有查询任务正在进行，请等待完成后再重试")
            return

        serial_number = self.huawei_serial_numbers[row_index]
        print(f"[重试] 开始重试华为设备查询 - 序号: {row_index + 1}, SN: {serial_number}")

        # 更新状态为正在查询
        status_item = QTableWidgetItem("正在重试...")
        status_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        status_item.setForeground(QColor("#0066cc"))  # 蓝色表示正在进行
        self.huawei_results_table.setItem(row_index, 2, status_item)

        # 清除操作列的按钮
        self.huawei_results_table.setCellWidget(row_index, 9, None)

        # 创建单个查询的工作线程
        use_proxy = self.huawei_use_proxy_checkbox.isChecked()
        proxy_key = self.huawei_proxy_key_input.text() if use_proxy else ""

        self.huawei_single_retry_worker = HuaweiWarrantyQueryWorker([serial_number], use_proxy, proxy_key)
        self.huawei_single_retry_worker.max_concurrent_queries = 1

        # 连接信号，但需要调整行索引
        self.huawei_single_retry_worker.query_completed.connect(
            lambda _, result: self.huawei_update_result(row_index, result)
        )

        # 启动工作线程
        self.huawei_single_retry_worker.start()

        self.status_bar.showMessage(f"正在重试华为设备序列号: {serial_number}")

    def huawei_retry_all_failed(self):
        """一键重试所有失败的华为设备查询"""
        print("[重试] 开始一键重试所有失败的华为设备查询")

        # 检查是否有查询正在进行
        if hasattr(self, 'huawei_query_worker') and self.huawei_query_worker and self.huawei_query_worker.isRunning():
            QMessageBox.warning(self, "重试提示", "当前有查询任务正在进行，请等待完成后再重试")
            return

        if not hasattr(self, 'huawei_serial_numbers'):
            QMessageBox.warning(self, "重试提示", "没有可重试的查询记录")
            return

        # 收集所有失败的序列号和对应的行索引
        failed_items = []
        for row in range(self.huawei_results_table.rowCount()):
            status_item = self.huawei_results_table.item(row, 2)
            if status_item:
                status = status_item.text()
                if status != "成功" and status != "等待查询" and status != "正在查询...":
                    if row < len(self.huawei_serial_numbers):
                        failed_items.append((row, self.huawei_serial_numbers[row]))

        if not failed_items:
            QMessageBox.information(self, "重试提示", "没有失败的查询需要重试")
            return

        # 确认重试
        reply = QMessageBox.question(
            self, "确认重试",
            f"确定要重试 {len(failed_items)} 个失败的华为设备查询吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.Yes
        )

        if reply != QMessageBox.StandardButton.Yes:
            return

        print(f"[重试] 准备重试 {len(failed_items)} 个失败的查询")

        # 禁用重试按钮
        self.huawei_retry_all_button.setEnabled(False)
        self.huawei_start_button.setEnabled(False)

        # 更新失败项的状态
        for row_index, _ in failed_items:
            status_item = QTableWidgetItem("等待重试...")
            status_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            status_item.setForeground(QColor("#0066cc"))
            self.huawei_results_table.setItem(row_index, 2, status_item)

            # 清除操作列的按钮
            self.huawei_results_table.setCellWidget(row_index, 9, None)

        # 创建重试查询的工作线程
        use_proxy = self.huawei_use_proxy_checkbox.isChecked()
        proxy_key = self.huawei_proxy_key_input.text() if use_proxy else ""

        # 解析并发数量
        concurrent_text = self.huawei_concurrent_input.currentText()
        concurrent_queries = int(concurrent_text.split()[0])

        # 只重试失败的序列号
        retry_serial_numbers = [sn for _, sn in failed_items]
        self.huawei_retry_row_mapping = {i: failed_items[i][0] for i in range(len(failed_items))}

        self.huawei_retry_worker = HuaweiWarrantyQueryWorker(retry_serial_numbers, use_proxy, proxy_key)
        self.huawei_retry_worker.max_concurrent_queries = concurrent_queries

        # 连接信号
        self.huawei_retry_worker.progress_updated.connect(self.huawei_retry_update_progress)
        self.huawei_retry_worker.query_completed.connect(self.huawei_retry_update_result)
        self.huawei_retry_worker.all_completed.connect(self.huawei_retry_completed)

        # 启动工作线程
        self.huawei_retry_worker.start()

        self.status_bar.showMessage(f"正在重试 {len(retry_serial_numbers)} 个失败的华为设备序列号...")

    def huawei_retry_update_progress(self, worker_row_index, status):
        """更新重试查询的进度"""
        if hasattr(self, 'huawei_retry_row_mapping') and worker_row_index in self.huawei_retry_row_mapping:
            actual_row_index = self.huawei_retry_row_mapping[worker_row_index]
            if 0 <= actual_row_index < self.huawei_results_table.rowCount():
                status_item = QTableWidgetItem(status)
                status_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                status_item.setForeground(QColor("#0066cc"))
                self.huawei_results_table.setItem(actual_row_index, 2, status_item)

    def huawei_retry_update_result(self, worker_row_index, result):
        """更新重试查询的结果"""
        if hasattr(self, 'huawei_retry_row_mapping') and worker_row_index in self.huawei_retry_row_mapping:
            actual_row_index = self.huawei_retry_row_mapping[worker_row_index]

            # 如果重试成功，需要更新统计数据
            old_status_item = self.huawei_results_table.item(actual_row_index, 2)
            old_status = old_status_item.text() if old_status_item else ""

            # 调用原有的结果更新方法
            self.huawei_update_result(actual_row_index, result)

            # 更新统计数据（因为原来是失败的，现在可能成功了）
            new_status = result["status"]
            if new_status == "成功" and old_status != "成功":
                # 从失败转为成功
                if self.huawei_failed_count > 0:
                    self.huawei_failed_count -= 1
                    self.huawei_failed_count_label.setText(str(self.huawei_failed_count))

    def huawei_retry_completed(self):
        """重试查询完成"""
        print("[重试] 华为设备重试查询全部完成")

        # 重新启用按钮
        self.huawei_start_button.setEnabled(True)

        # 检查是否还有失败的查询
        failed_count = 0
        for row in range(self.huawei_results_table.rowCount()):
            status_item = self.huawei_results_table.item(row, 2)
            if status_item:
                status = status_item.text()
                if status != "成功" and status != "等待查询" and status != "正在查询...":
                    failed_count += 1

        if failed_count > 0:
            self.huawei_retry_all_button.setEnabled(True)
        else:
            self.huawei_retry_all_button.setEnabled(False)

        # 清理工作线程
        self.huawei_retry_worker = None
        if hasattr(self, 'huawei_retry_row_mapping'):
            delattr(self, 'huawei_retry_row_mapping')

        # 更新状态栏
        total = len(self.huawei_serial_numbers)
        success = self.huawei_success_count
        failed = self.huawei_failed_count
        success_rate = (success / total * 100) if total > 0 else 0

        self.status_bar.showMessage(
            f"华为设备重试完成 | 总数: {total} | 成功: {success} | 失败: {failed} | 成功率: {success_rate:.1f}%"
        )

    def huawei_export_results(self):
        """Export Huawei results to Excel file"""
        if self.huawei_results_table.rowCount() == 0:
            QMessageBox.information(self, "导出提示", "没有可导出的结果")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出结果", f"huawei_query_results_{time.strftime('%Y%m%d_%H%M%S')}.xlsx",
            "Excel Files (*.xlsx)"
        )

        if not file_path:
            return

        try:
            # Create DataFrame from table data
            data = []
            for row in range(self.huawei_results_table.rowCount()):
                row_data = {}
                for col in range(self.huawei_results_table.columnCount() - 1):  # Skip the "操作" column
                    header_item = self.huawei_results_table.horizontalHeaderItem(col)
                    header = header_item.text() if header_item else f"列{col}"
                    item = self.huawei_results_table.item(row, col)
                    row_data[header] = item.text() if item else ""
                data.append(row_data)

            df = pd.DataFrame(data)
            df.to_excel(file_path, index=False)

            QMessageBox.information(self, "导出成功", f"结果已成功导出到: {file_path}")
        except Exception as e:
            QMessageBox.warning(self, "导出错误", f"导出结果时出错: {str(e)}")

    def honor_update_concurrent_tip(self):
        """更新荣耀设备查询的并发提示"""
        concurrent_text = self.honor_concurrent_input.currentText()
        concurrent_num = int(concurrent_text.split()[0])  # 提取第一个数字

        if concurrent_num <= 2:
            tip = "提示: 当前使用低并发模式，查询速度较慢但稳定性最佳"
        elif concurrent_num <= 8:
            tip = "提示: 当前使用中等并发模式，平衡了速度和稳定性"
        else:
            tip = "提示: 当前使用高并发模式，查询速度快但可能遇到更多验证码或限制"

        self.honor_concurrent_tip_label.setText(tip)

    def huawei_update_concurrent_tip(self):
        """更新华为设备查询的并发提示"""
        concurrent_text = self.huawei_concurrent_input.currentText()
        concurrent_num = int(concurrent_text.split()[0])  # 提取第一个数字

        if concurrent_num <= 2:
            tip = "提示: 当前使用低并发模式，查询速度较慢但稳定性最佳"
        elif concurrent_num <= 8:
            tip = "提示: 当前使用中等并发模式，平衡了速度和稳定性"
        else:
            tip = "提示: 当前使用高并发模式，查询速度快但可能遇到更多验证码或限制"

        self.huawei_concurrent_tip_label.setText(tip)

    def closeEvent(self, a0):
        """处理窗口关闭事件"""
        if a0 is None:
            return

        # 检查是否有查询任务正在进行
        has_running_tasks = bool(
            (hasattr(self, 'honor_query_worker') and self.honor_query_worker and self.honor_query_worker.isRunning()) or
            (hasattr(self, 'honor_single_retry_worker') and self.honor_single_retry_worker and self.honor_single_retry_worker.isRunning()) or
            (hasattr(self, 'honor_retry_worker') and self.honor_retry_worker and self.honor_retry_worker.isRunning()) or
            (hasattr(self, 'huawei_query_worker') and self.huawei_query_worker and self.huawei_query_worker.isRunning()) or
            (hasattr(self, 'huawei_single_retry_worker') and self.huawei_single_retry_worker and self.huawei_single_retry_worker.isRunning()) or
            (hasattr(self, 'huawei_retry_worker') and self.huawei_retry_worker and self.huawei_retry_worker.isRunning())
        )

        # 创建美化的退出确认对话框
        exit_dialog = AnimatedExitDialog(self, has_running_tasks)

        # 居中显示对话框
        exit_dialog.move(
            self.x() + (self.width() - exit_dialog.width()) // 2,
            self.y() + (self.height() - exit_dialog.height()) // 2
        )

        # 显示对话框并获取结果
        result = exit_dialog.exec()

        if result == QDialog.DialogCode.Accepted:
            # 停止所有查询线程
            if hasattr(self, 'honor_query_worker') and self.honor_query_worker and self.honor_query_worker.isRunning():
                self.honor_query_worker.stop()
                print("[退出] 停止荣耀设备查询线程")

            if hasattr(self, 'honor_single_retry_worker') and self.honor_single_retry_worker and self.honor_single_retry_worker.isRunning():
                self.honor_single_retry_worker.stop()
                print("[退出] 停止荣耀设备单个重试线程")

            if hasattr(self, 'honor_retry_worker') and self.honor_retry_worker and self.honor_retry_worker.isRunning():
                self.honor_retry_worker.stop()
                print("[退出] 停止荣耀设备批量重试线程")

            if hasattr(self, 'huawei_query_worker') and self.huawei_query_worker and self.huawei_query_worker.isRunning():
                self.huawei_query_worker.stop()
                print("[退出] 停止华为设备查询线程")

            if hasattr(self, 'huawei_single_retry_worker') and self.huawei_single_retry_worker and self.huawei_single_retry_worker.isRunning():
                self.huawei_single_retry_worker.stop()
                print("[退出] 停止华为设备单个重试线程")

            if hasattr(self, 'huawei_retry_worker') and self.huawei_retry_worker and self.huawei_retry_worker.isRunning():
                self.huawei_retry_worker.stop()
                print("[退出] 停止华为设备批量重试线程")

            print("[退出] 用户确认退出，程序即将关闭")
            a0.accept()  # 接受关闭事件
        else:
            print("[退出] 用户取消退出")
            a0.ignore()  # 忽略关闭事件，窗口保持打开





def resource_path(relative_path):
    """获取资源的绝对路径，适用于开发环境和PyInstaller打包后的环境"""
    try:
        # PyInstaller创建临时文件夹，将路径存储在_MEIPASS中
        base_path = getattr(sys, '_MEIPASS', os.path.abspath("."))
    except Exception:
        # 如果不是通过PyInstaller打包，则使用当前文件夹
        base_path = os.path.abspath(".")

    return os.path.join(base_path, relative_path)

def main():
    app = QApplication(sys.argv)
    window = HonorWarrantyApp()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
